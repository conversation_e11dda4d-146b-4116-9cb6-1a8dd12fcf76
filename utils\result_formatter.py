"""
Result formatting utilities
"""
import json
from typing import Dict, List, Any
from datetime import datetime
import os


class ResultFormatter:
    """Format and export analysis results"""
    
    def __init__(self):
        """Initialize the result formatter"""
        pass
    
    def format_analysis_result(self, 
                             image_path: str,
                             detections: List[Dict],
                             style_results: List[Dict],
                             material_results: List[Dict],
                             color_results: List[Dict],
                             composition_analysis: Dict) -> Dict:
        """
        Format complete analysis results
        
        Args:
            image_path: Path to analyzed image
            detections: Furniture detection results
            style_results: Style classification results
            material_results: Material classification results
            color_results: Color analysis results
            composition_analysis: Furniture composition analysis
            
        Returns:
            Formatted analysis results
        """
        # Create main result structure
        result = {
            'image_info': {
                'path': image_path,
                'filename': os.path.basename(image_path),
                'analysis_timestamp': datetime.now().isoformat()
            },
            'furniture_detection': {
                'total_items': len(detections),
                'detected_furniture': detections
            },
            'composition_analysis': composition_analysis,
            'furniture_details': []
        }
        
        # Format individual furniture items
        for i, detection in enumerate(detections):
            furniture_item = {
                'item_id': i + 1,
                'furniture_type': detection.get('class_name', 'unknown'),
                'detection_confidence': detection.get('confidence', 0.0),
                'bounding_box': detection.get('bbox', []),
                'area': detection.get('area', 0)
            }
            
            # Add style information if available
            if i < len(style_results):
                style_result = style_results[i]
                furniture_item['style'] = {
                    'predicted_style': style_result.get('predicted_style', 'unknown'),
                    'confidence': style_result.get('confidence', 0.0),
                    'all_style_predictions': style_result.get('all_predictions', {}),
                    'classification_method': style_result.get('method', 'unknown')
                }
            
            # Add material information if available
            if i < len(material_results):
                material_result = material_results[i]
                furniture_item['material'] = {
                    'predicted_material': material_result.get('predicted_material', 'unknown'),
                    'confidence': material_result.get('confidence', 0.0),
                    'all_material_predictions': material_result.get('all_predictions', {}),
                    'texture_features': material_result.get('texture_features', {}),
                    'surface_properties': material_result.get('surface_features', {})
                }
            
            # Add color information if available
            if i < len(color_results):
                color_result = color_results[i]
                furniture_item['colors'] = {
                    'primary_color': color_result.get('primary_color', 'unknown'),
                    'dominant_colors': color_result.get('dominant_colors_names', []),
                    'color_harmony': color_result.get('color_harmony', {}),
                    'brightness_level': color_result.get('brightness_level', 'medium'),
                    'saturation_level': color_result.get('saturation_level', 'medium')
                }
            
            # Determine usage scenario
            furniture_item['usage_scenario'] = self._determine_usage_scenario(
                furniture_item.get('furniture_type', ''),
                composition_analysis
            )
            
            result['furniture_details'].append(furniture_item)
        
        # Add overall summary
        result['summary'] = self._create_summary(result)
        
        return result
    
    def _determine_usage_scenario(self, furniture_type: str, composition: Dict) -> str:
        """Determine the usage scenario for furniture"""
        # Mapping of furniture types to common scenarios
        scenario_mapping = {
            'sofa': 'living_room',
            'couch': 'living_room',
            'chair': 'dining_room',
            'dining table': 'dining_room',
            'bed': 'bedroom',
            'nightstand': 'bedroom',
            'dresser': 'bedroom',
            'desk': 'office',
            'bookshelf': 'study',
            'coffee table': 'living_room',
            'tv stand': 'living_room',
            'wardrobe': 'bedroom',
            'cabinet': 'kitchen',
            'stool': 'kitchen'
        }
        
        # Check composition type for context
        composition_type = composition.get('composition_type', '')
        
        if composition_type == 'dining_set':
            return 'dining_room'
        elif composition_type == 'living_room_set':
            return 'living_room'
        elif composition_type == 'bedroom_set':
            return 'bedroom'
        
        # Default to furniture type mapping
        return scenario_mapping.get(furniture_type.lower(), 'general')
    
    def _create_summary(self, result: Dict) -> Dict:
        """Create a summary of the analysis results"""
        furniture_details = result.get('furniture_details', [])
        composition = result.get('composition_analysis', {})
        
        # Count furniture types
        furniture_types = {}
        styles = {}
        materials = {}
        colors = {}
        scenarios = {}
        
        for item in furniture_details:
            # Count furniture types
            ftype = item.get('furniture_type', 'unknown')
            furniture_types[ftype] = furniture_types.get(ftype, 0) + 1
            
            # Count styles
            style = item.get('style', {}).get('predicted_style', 'unknown')
            styles[style] = styles.get(style, 0) + 1
            
            # Count materials
            material = item.get('material', {}).get('predicted_material', 'unknown')
            materials[material] = materials.get(material, 0) + 1
            
            # Count colors
            color = item.get('colors', {}).get('primary_color', 'unknown')
            colors[color] = colors.get(color, 0) + 1
            
            # Count scenarios
            scenario = item.get('usage_scenario', 'unknown')
            scenarios[scenario] = scenarios.get(scenario, 0) + 1
        
        # Determine dominant characteristics
        dominant_style = max(styles.items(), key=lambda x: x[1])[0] if styles else 'unknown'
        dominant_material = max(materials.items(), key=lambda x: x[1])[0] if materials else 'unknown'
        dominant_color = max(colors.items(), key=lambda x: x[1])[0] if colors else 'unknown'
        primary_scenario = max(scenarios.items(), key=lambda x: x[1])[0] if scenarios else 'unknown'
        
        summary = {
            'total_furniture_items': len(furniture_details),
            'is_furniture_set': composition.get('is_composition', False),
            'set_type': composition.get('composition_type', 'single_item'),
            'dominant_style': dominant_style,
            'dominant_material': dominant_material,
            'dominant_color': dominant_color,
            'primary_usage_scenario': primary_scenario,
            'furniture_type_distribution': furniture_types,
            'style_distribution': styles,
            'material_distribution': materials,
            'color_distribution': colors,
            'scenario_distribution': scenarios
        }
        
        return summary
    
    def export_to_json(self, result: Dict, output_path: str) -> bool:
        """
        Export results to JSON file
        
        Args:
            result: Analysis results
            output_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error exporting to JSON: {e}")
            return False
    
    def create_readable_report(self, result: Dict) -> str:
        """
        Create a human-readable report
        
        Args:
            result: Analysis results
            
        Returns:
            Formatted text report
        """
        report_lines = []
        
        # Header
        image_info = result.get('image_info', {})
        report_lines.append("=" * 60)
        report_lines.append("FURNITURE ANALYSIS REPORT")
        report_lines.append("=" * 60)
        report_lines.append(f"Image: {image_info.get('filename', 'Unknown')}")
        report_lines.append(f"Analysis Date: {image_info.get('analysis_timestamp', 'Unknown')}")
        report_lines.append("")
        
        # Summary
        summary = result.get('summary', {})
        report_lines.append("SUMMARY")
        report_lines.append("-" * 20)
        report_lines.append(f"Total Furniture Items: {summary.get('total_furniture_items', 0)}")
        report_lines.append(f"Is Furniture Set: {'Yes' if summary.get('is_furniture_set', False) else 'No'}")
        if summary.get('is_furniture_set', False):
            report_lines.append(f"Set Type: {summary.get('set_type', 'Unknown')}")
        report_lines.append(f"Dominant Style: {summary.get('dominant_style', 'Unknown')}")
        report_lines.append(f"Dominant Material: {summary.get('dominant_material', 'Unknown')}")
        report_lines.append(f"Dominant Color: {summary.get('dominant_color', 'Unknown')}")
        report_lines.append(f"Primary Usage Scenario: {summary.get('primary_usage_scenario', 'Unknown')}")
        report_lines.append("")
        
        # Individual furniture items
        furniture_details = result.get('furniture_details', [])
        if furniture_details:
            report_lines.append("DETAILED ANALYSIS")
            report_lines.append("-" * 20)
            
            for i, item in enumerate(furniture_details, 1):
                report_lines.append(f"\nItem {i}: {item.get('furniture_type', 'Unknown').title()}")
                report_lines.append(f"  Detection Confidence: {item.get('detection_confidence', 0):.2f}")
                
                # Style information
                style_info = item.get('style', {})
                if style_info:
                    report_lines.append(f"  Style: {style_info.get('predicted_style', 'Unknown')} "
                                      f"(Confidence: {style_info.get('confidence', 0):.2f})")
                
                # Material information
                material_info = item.get('material', {})
                if material_info:
                    report_lines.append(f"  Material: {material_info.get('predicted_material', 'Unknown')} "
                                      f"(Confidence: {material_info.get('confidence', 0):.2f})")
                
                # Color information
                color_info = item.get('colors', {})
                if color_info:
                    report_lines.append(f"  Primary Color: {color_info.get('primary_color', 'Unknown')}")
                    dominant_colors = color_info.get('dominant_colors', [])
                    if dominant_colors:
                        report_lines.append(f"  Color Palette: {', '.join(dominant_colors[:3])}")
                
                # Usage scenario
                report_lines.append(f"  Usage Scenario: {item.get('usage_scenario', 'Unknown')}")
        
        return "\n".join(report_lines)
    
    def export_readable_report(self, result: Dict, output_path: str) -> bool:
        """
        Export readable report to text file
        
        Args:
            result: Analysis results
            output_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            report = self.create_readable_report(result)
            
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write text file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return True
            
        except Exception as e:
            print(f"Error exporting readable report: {e}")
            return False
