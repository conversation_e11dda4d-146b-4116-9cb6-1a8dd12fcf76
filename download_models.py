#!/usr/bin/env python3
"""
下载和验证所有必要的模型文件
"""
import os
import sys
import torch
from pathlib import Path
from ultralytics import YOLO
from torchvision import models
import requests
from tqdm import tqdm

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def download_file(url, filename, description=""):
    """下载文件并显示进度条"""
    print(f"正在下载 {description}: {filename}")
    
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                file.write(chunk)
                pbar.update(len(chunk))
    
    print(f"✓ {description} 下载完成")

def download_yolo_models():
    """下载YOLO模型"""
    print("\n=== 下载YOLO模型 ===")
    
    models_dir = Path("data/models")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # YOLOv8n模型 (最轻量级)
    yolo_model_path = models_dir / "yolov8n.pt"
    
    if not yolo_model_path.exists():
        print("下载YOLOv8n模型...")
        try:
            # 使用ultralytics自动下载
            model = YOLO('yolov8n.pt')
            print("✓ YOLOv8n模型下载完成")
        except Exception as e:
            print(f"❌ YOLOv8n模型下载失败: {e}")
            return False
    else:
        print("✓ YOLOv8n模型已存在")
    
    return True

def download_resnet_models():
    """下载ResNet预训练模型"""
    print("\n=== 下载ResNet模型 ===")
    
    try:
        print("下载ResNet18预训练模型...")
        # 这会自动下载预训练权重到torch缓存目录
        model = models.resnet18(pretrained=True)
        print("✓ ResNet18预训练模型下载完成")
        return True
    except Exception as e:
        print(f"❌ ResNet18模型下载失败: {e}")
        return False

def verify_models():
    """验证模型是否可以正常加载"""
    print("\n=== 验证模型 ===")
    
    success = True
    
    # 验证YOLO模型
    try:
        from models.yolo_detector import FurnitureDetector
        detector = FurnitureDetector()
        print("✓ YOLO检测器初始化成功")
    except Exception as e:
        print(f"❌ YOLO检测器初始化失败: {e}")
        success = False
    
    # 验证风格分类器
    try:
        from models.style_classifier import StyleClassifier
        classifier = StyleClassifier()
        print("✓ 风格分类器初始化成功")
    except Exception as e:
        print(f"❌ 风格分类器初始化失败: {e}")
        success = False
    
    # 验证材质分类器
    try:
        from models.material_classifier import MaterialClassifier
        material_classifier = MaterialClassifier()
        print("✓ 材质分类器初始化成功")
    except Exception as e:
        print(f"❌ 材质分类器初始化失败: {e}")
        success = False
    
    # 验证颜色分析器
    try:
        from models.color_analyzer import ColorAnalyzer
        color_analyzer = ColorAnalyzer()
        print("✓ 颜色分析器初始化成功")
    except Exception as e:
        print(f"❌ 颜色分析器初始化失败: {e}")
        success = False
    
    return success

def check_gpu_support():
    """检查GPU支持"""
    print("\n=== GPU支持检查 ===")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA可用")
        print(f"  GPU设备数量: {torch.cuda.device_count()}")
        print(f"  当前GPU: {torch.cuda.get_device_name(0)}")
        print(f"  CUDA版本: {torch.version.cuda}")
    else:
        print("⚠ CUDA不可用，将使用CPU模式")
        print("  如需GPU加速，请安装CUDA版本的PyTorch")

def create_model_info():
    """创建模型信息文件"""
    model_info = {
        "models": {
            "yolo": {
                "name": "YOLOv8n",
                "file": "yolov8n.pt",
                "description": "轻量级目标检测模型，用于家具检测",
                "size": "约6MB"
            },
            "resnet": {
                "name": "ResNet18",
                "description": "预训练的图像分类模型，用于风格和材质分类",
                "size": "约45MB"
            }
        },
        "download_status": "completed",
        "last_updated": str(Path(__file__).stat().st_mtime)
    }
    
    import json
    with open("data/models/model_info.json", "w", encoding="utf-8") as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    print("✓ 模型信息文件已创建")

def main():
    """主函数"""
    print("=" * 60)
    print("RecoImage 模型下载工具")
    print("=" * 60)
    
    # 检查GPU支持
    check_gpu_support()
    
    # 下载模型
    yolo_success = download_yolo_models()
    resnet_success = download_resnet_models()
    
    if yolo_success and resnet_success:
        print("\n✅ 所有模型下载完成！")
        
        # 验证模型
        if verify_models():
            print("\n✅ 所有模型验证通过！")
            create_model_info()
            
            print("\n" + "=" * 60)
            print("🎉 模型下载和验证完成！")
            print("现在您可以运行以下命令启动应用:")
            print("  py -3.13 -m streamlit run web_app.py")
            print("=" * 60)
        else:
            print("\n❌ 部分模型验证失败")
            return 1
    else:
        print("\n❌ 模型下载失败")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断下载")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 下载过程中出现错误: {e}")
        sys.exit(1)
