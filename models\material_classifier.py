"""
Furniture material classification module
"""
import cv2
import numpy as np
from typing import Dict, List
from sklearn.cluster import KMeans
import torch
import torch.nn as nn
from torchvision import transforms, models
from PIL import Image

from config.settings import FURNITURE_MATERIALS, MATERIAL_CONFIDENCE_THRESHOLD


class MaterialClassifier:
    """Furniture material classifier"""
    
    def __init__(self):
        """Initialize the material classifier"""
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.materials = FURNITURE_MATERIALS
        self.confidence_threshold = MATERIAL_CONFIDENCE_THRESHOLD
        
        # Material texture characteristics
        self.material_features = {
            'wood': {
                'texture_patterns': ['grain', 'rings'],
                'color_range': [(139, 69, 19), (160, 82, 45), (210, 180, 140)],  # <PERSON> tones
                'surface_type': 'natural'
            },
            'metal': {
                'texture_patterns': ['smooth', 'reflective'],
                'color_range': [(192, 192, 192), (128, 128, 128), (255, 215, 0)],  # Silver, gray, gold
                'surface_type': 'reflective'
            },
            'fabric': {
                'texture_patterns': ['woven', 'soft'],
                'color_range': 'varied',
                'surface_type': 'soft'
            },
            'leather': {
                'texture_patterns': ['smooth', 'textured'],
                'color_range': [(139, 69, 19), (160, 82, 45), (0, 0, 0)],  # Brown, black
                'surface_type': 'smooth'
            },
            'plastic': {
                'texture_patterns': ['smooth', 'uniform'],
                'color_range': 'varied',
                'surface_type': 'smooth'
            },
            'glass': {
                'texture_patterns': ['transparent', 'reflective'],
                'color_range': [(255, 255, 255), (240, 248, 255)],  # Clear, light blue
                'surface_type': 'transparent'
            }
        }
    
    def classify_material(self, image: np.ndarray) -> Dict:
        """
        Classify furniture material from image
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            Dictionary with material predictions and confidence scores
        """
        try:
            # Convert BGR to RGB
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # Analyze texture features
            texture_analysis = self._analyze_texture(image_rgb)
            
            # Analyze color features
            color_analysis = self._analyze_material_colors(image_rgb)
            
            # Analyze surface properties
            surface_analysis = self._analyze_surface_properties(image_rgb)
            
            # Combine analyses for material prediction
            material_scores = self._calculate_material_scores(
                texture_analysis, color_analysis, surface_analysis
            )
            
            # Get top prediction
            top_material = max(material_scores.items(), key=lambda x: x[1])
            
            return {
                'predicted_material': top_material[0],
                'confidence': top_material[1],
                'all_predictions': material_scores,
                'texture_features': texture_analysis,
                'color_features': color_analysis,
                'surface_features': surface_analysis
            }
            
        except Exception as e:
            print(f"Error in material classification: {e}")
            return self._fallback_material_classification(image)
    
    def _analyze_texture(self, image: np.ndarray) -> Dict:
        """Analyze texture patterns in the image"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Calculate texture features using Local Binary Pattern (LBP) approximation
        texture_features = {}
        
        # Calculate gradient magnitude for texture analysis
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # Texture metrics
        texture_features['gradient_variance'] = np.var(gradient_magnitude)
        texture_features['gradient_mean'] = np.mean(gradient_magnitude)
        
        # Edge density
        edges = cv2.Canny(gray, 50, 150)
        texture_features['edge_density'] = np.sum(edges > 0) / edges.size
        
        # Smoothness (inverse of texture)
        texture_features['smoothness'] = 1.0 / (1.0 + texture_features['gradient_variance'])
        
        # Pattern regularity using autocorrelation
        texture_features['pattern_regularity'] = self._calculate_pattern_regularity(gray)
        
        return texture_features
    
    def _calculate_pattern_regularity(self, gray_image: np.ndarray) -> float:
        """Calculate pattern regularity in the image"""
        # Resize for faster computation
        small_gray = cv2.resize(gray_image, (64, 64))
        
        # Calculate autocorrelation
        f_transform = np.fft.fft2(small_gray)
        autocorr = np.fft.ifft2(f_transform * np.conj(f_transform))
        autocorr = np.real(autocorr)
        
        # Normalize
        autocorr = autocorr / autocorr[0, 0]
        
        # Calculate regularity as the sum of significant autocorrelation values
        threshold = 0.1
        regularity = np.sum(np.abs(autocorr) > threshold) / autocorr.size
        
        return float(regularity)
    
    def _analyze_material_colors(self, image: np.ndarray) -> Dict:
        """Analyze colors specific to material identification"""
        # Resize for faster processing
        small_image = cv2.resize(image, (100, 100))
        
        # Flatten image for color clustering
        pixels = small_image.reshape(-1, 3)
        
        # Use KMeans to find dominant colors
        try:
            kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
            kmeans.fit(pixels)
            dominant_colors = kmeans.cluster_centers_
        except:
            # Fallback to simple color analysis
            dominant_colors = [np.mean(pixels, axis=0)]
        
        # Calculate color statistics
        color_features = {
            'dominant_colors': dominant_colors.tolist(),
            'color_variance': np.var(pixels, axis=0).tolist(),
            'brightness': np.mean(np.sum(pixels, axis=1)),
            'saturation': self._calculate_saturation(pixels)
        }
        
        return color_features
    
    def _calculate_saturation(self, pixels: np.ndarray) -> float:
        """Calculate average saturation of pixels"""
        # Convert to HSV
        hsv_pixels = []
        for pixel in pixels[::10]:  # Sample every 10th pixel for speed
            hsv = cv2.cvtColor(np.uint8([[pixel]]), cv2.COLOR_RGB2HSV)
            hsv_pixels.append(hsv[0, 0, 1])
        
        return float(np.mean(hsv_pixels)) if hsv_pixels else 0.0
    
    def _analyze_surface_properties(self, image: np.ndarray) -> Dict:
        """Analyze surface properties like reflectivity and transparency"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Calculate brightness statistics
        brightness_stats = {
            'mean_brightness': np.mean(gray),
            'brightness_variance': np.var(gray),
            'max_brightness': np.max(gray),
            'min_brightness': np.min(gray)
        }
        
        # Estimate reflectivity (high brightness variance might indicate reflective surfaces)
        reflectivity_score = brightness_stats['brightness_variance'] / 255.0
        
        # Estimate transparency (low contrast might indicate transparent materials)
        contrast = brightness_stats['max_brightness'] - brightness_stats['min_brightness']
        transparency_score = 1.0 - (contrast / 255.0)
        
        surface_features = {
            'reflectivity_score': float(reflectivity_score),
            'transparency_score': float(transparency_score),
            'contrast': float(contrast),
            'brightness_stats': brightness_stats
        }
        
        return surface_features
    
    def _calculate_material_scores(self, texture_analysis: Dict, 
                                 color_analysis: Dict, 
                                 surface_analysis: Dict) -> Dict:
        """Calculate material probability scores"""
        material_scores = {}
        
        for material in self.materials:
            score = 0.0
            
            if material == 'wood':
                # Wood typically has medium texture, brown colors, natural surface
                score += 0.3 * (0.3 < texture_analysis['gradient_variance'] < 0.7)
                score += 0.3 * self._check_brown_colors(color_analysis['dominant_colors'])
                score += 0.2 * (0.2 < surface_analysis['reflectivity_score'] < 0.6)
                score += 0.2 * (texture_analysis['pattern_regularity'] > 0.1)
                
            elif material == 'metal':
                # Metal typically has smooth texture, metallic colors, high reflectivity
                score += 0.4 * (surface_analysis['reflectivity_score'] > 0.7)
                score += 0.3 * (texture_analysis['smoothness'] > 0.6)
                score += 0.3 * self._check_metallic_colors(color_analysis['dominant_colors'])
                
            elif material == 'fabric':
                # Fabric has medium texture, varied colors, low reflectivity
                score += 0.4 * (0.3 < texture_analysis['gradient_variance'] < 0.8)
                score += 0.3 * (surface_analysis['reflectivity_score'] < 0.4)
                score += 0.3 * (color_analysis['saturation'] > 50)
                
            elif material == 'leather':
                # Leather has smooth texture, typically brown/black, medium reflectivity
                score += 0.3 * (texture_analysis['smoothness'] > 0.5)
                score += 0.4 * self._check_leather_colors(color_analysis['dominant_colors'])
                score += 0.3 * (0.3 < surface_analysis['reflectivity_score'] < 0.7)
                
            elif material == 'plastic':
                # Plastic has very smooth texture, varied colors, medium reflectivity
                score += 0.4 * (texture_analysis['smoothness'] > 0.8)
                score += 0.3 * (0.4 < surface_analysis['reflectivity_score'] < 0.8)
                score += 0.3 * (texture_analysis['pattern_regularity'] < 0.1)
                
            elif material == 'glass':
                # Glass has very smooth texture, light colors, high transparency/reflectivity
                score += 0.3 * (texture_analysis['smoothness'] > 0.9)
                score += 0.4 * (surface_analysis['transparency_score'] > 0.6 or 
                              surface_analysis['reflectivity_score'] > 0.8)
                score += 0.3 * (color_analysis['brightness'] > 200)
            
            # Ensure score is between 0 and 1
            material_scores[material] = max(0.0, min(1.0, score))
        
        return material_scores
    
    def _check_brown_colors(self, dominant_colors: List) -> float:
        """Check if dominant colors include brown tones"""
        brown_score = 0.0
        for color in dominant_colors:
            r, g, b = color
            # Check if color is in brown range
            if 100 < r < 200 and 50 < g < 150 and 20 < b < 100:
                brown_score += 0.5
        return min(1.0, brown_score)
    
    def _check_metallic_colors(self, dominant_colors: List) -> float:
        """Check if dominant colors include metallic tones"""
        metallic_score = 0.0
        for color in dominant_colors:
            r, g, b = color
            # Check for gray/silver tones or gold tones
            if (abs(r - g) < 30 and abs(g - b) < 30 and r > 100) or \
               (r > 200 and g > 150 and b < 100):  # Gold-ish
                metallic_score += 0.5
        return min(1.0, metallic_score)
    
    def _check_leather_colors(self, dominant_colors: List) -> float:
        """Check if dominant colors include leather tones"""
        leather_score = 0.0
        for color in dominant_colors:
            r, g, b = color
            # Check for brown or black leather colors
            if (100 < r < 180 and 50 < g < 120 and 20 < b < 80) or \
               (r < 50 and g < 50 and b < 50):  # Black
                leather_score += 0.5
        return min(1.0, leather_score)
    
    def _fallback_material_classification(self, image: np.ndarray) -> Dict:
        """Fallback material classification"""
        return {
            'predicted_material': 'wood',
            'confidence': 0.3,
            'all_predictions': {'wood': 0.3, 'fabric': 0.2, 'metal': 0.1},
            'method': 'fallback'
        }
