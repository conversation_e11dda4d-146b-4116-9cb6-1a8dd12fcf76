"""
Configuration settings for the furniture recognition system
"""
import os
from pathlib import Path

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent

# Model paths
MODELS_DIR = PROJECT_ROOT / "data" / "models"
YOLO_MODEL_PATH = MODELS_DIR / "yolo_furniture.pt"

# Data directories
DATA_DIR = PROJECT_ROOT / "data"
SAMPLE_IMAGES_DIR = DATA_DIR / "sample_images"

# YOLO settings
YOLO_CONFIDENCE_THRESHOLD = 0.5
YOLO_IOU_THRESHOLD = 0.45

# Furniture categories
FURNITURE_CATEGORIES = [
    "sofa", "chair", "table", "bed", "desk", "cabinet", "shelf", 
    "dresser", "nightstand", "wardrobe", "dining_table", "coffee_table",
    "bookshelf", "tv_stand", "ottoman", "bench", "stool"
]

# Style categories
FURNITURE_STYLES = [
    "modern", "contemporary", "traditional", "chinese", "american", 
    "french", "vintage", "mid_century", "song_aesthetic", "minimalist",
    "industrial", "scandinavian", "rustic", "baroque", "art_deco"
]

# Material categories
FURNITURE_MATERIALS = [
    "wood", "metal", "fabric", "leather", "plastic", "glass", 
    "rattan", "bamboo", "marble", "stone", "composite", "velvet",
    "linen", "cotton", "synthetic"
]

# Color categories
COLOR_CATEGORIES = [
    "white", "black", "brown", "gray", "beige", "cream", "red", 
    "blue", "green", "yellow", "orange", "purple", "pink", "gold", "silver"
]

# Usage scenarios
USAGE_SCENARIOS = [
    "living_room", "bedroom", "dining_room", "kitchen", "office", 
    "study", "bathroom", "outdoor", "entryway", "children_room"
]

# Image processing settings
MAX_IMAGE_SIZE = (1024, 1024)
MIN_IMAGE_SIZE = (224, 224)

# Model confidence thresholds
STYLE_CONFIDENCE_THRESHOLD = 0.6
MATERIAL_CONFIDENCE_THRESHOLD = 0.5
COLOR_CONFIDENCE_THRESHOLD = 0.4
