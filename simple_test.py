"""
简化测试脚本 - 检查基本功能和依赖
"""
import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("Python版本检查")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    
    version_info = sys.version_info
    if version_info.major >= 3 and version_info.minor >= 8:
        print("✓ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("✗ Python版本过低，需要3.8或更高版本")
        return False

def test_basic_imports():
    """测试基础库导入"""
    print("\n" + "=" * 50)
    print("基础库导入测试")
    print("=" * 50)
    
    basic_libs = {
        'os': 'os',
        'sys': 'sys', 
        'pathlib': 'pathlib',
        'json': 'json',
        'datetime': 'datetime'
    }
    
    success_count = 0
    for lib_name, import_name in basic_libs.items():
        try:
            __import__(import_name)
            print(f"✓ {lib_name}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {lib_name}: {e}")
    
    return success_count == len(basic_libs)

def test_required_imports():
    """测试必需的第三方库"""
    print("\n" + "=" * 50)
    print("必需库导入测试")
    print("=" * 50)
    
    required_libs = {
        'numpy': 'numpy',
        'opencv': 'cv2',
        'PIL': 'PIL',
        'ultralytics': 'ultralytics'
    }
    
    success_count = 0
    for lib_name, import_name in required_libs.items():
        try:
            lib = __import__(import_name)
            if hasattr(lib, '__version__'):
                version = lib.__version__
            else:
                version = "未知版本"
            print(f"✓ {lib_name}: {version}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {lib_name}: 未安装 - {e}")
    
    return success_count, len(required_libs)

def test_optional_imports():
    """测试可选的第三方库"""
    print("\n" + "=" * 50)
    print("可选库导入测试")
    print("=" * 50)
    
    optional_libs = {
        'torch': 'torch',
        'torchvision': 'torchvision', 
        'sklearn': 'sklearn',
        'pandas': 'pandas',
        'matplotlib': 'matplotlib',
        'streamlit': 'streamlit',
        'colorthief': 'colorthief',
        'skimage': 'skimage',
        'tqdm': 'tqdm',
        'requests': 'requests',
        'yaml': 'yaml'
    }
    
    success_count = 0
    for lib_name, import_name in optional_libs.items():
        try:
            lib = __import__(import_name)
            if hasattr(lib, '__version__'):
                version = lib.__version__
            else:
                version = "未知版本"
            print(f"✓ {lib_name}: {version}")
            success_count += 1
        except ImportError:
            print(f"○ {lib_name}: 未安装 (可选)")
    
    return success_count, len(optional_libs)

def test_project_structure():
    """测试项目结构"""
    print("\n" + "=" * 50)
    print("项目结构检查")
    print("=" * 50)
    
    required_files = [
        'main.py',
        'web_app.py',
        'requirements.txt',
        'README.md',
        'config/settings.py',
        'models/yolo_detector.py',
        'models/style_classifier.py',
        'models/material_classifier.py',
        'models/color_analyzer.py',
        'utils/image_processor.py',
        'utils/result_formatter.py'
    ]
    
    success_count = 0
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
            success_count += 1
        else:
            print(f"✗ {file_path}: 文件不存在")
    
    return success_count, len(required_files)

def test_yolo_model():
    """测试YOLO模型下载"""
    print("\n" + "=" * 50)
    print("YOLO模型测试")
    print("=" * 50)
    
    try:
        from ultralytics import YOLO
        print("正在下载YOLOv8模型...")
        
        # 尝试加载YOLOv8n模型（最小版本）
        model = YOLO('yolov8n.pt')
        print("✓ YOLOv8n模型下载成功")
        
        # 检查模型信息
        print(f"模型类别数: {len(model.names)}")
        print(f"模型设备: {model.device}")
        
        return True
        
    except ImportError:
        print("✗ ultralytics未安装，无法测试YOLO模型")
        return False
    except Exception as e:
        print(f"✗ YOLO模型测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n" + "=" * 50)
    print("基本功能测试")
    print("=" * 50)
    
    try:
        # 测试配置导入
        sys.path.append('.')
        from config.settings import FURNITURE_CATEGORIES, FURNITURE_STYLES
        print(f"✓ 配置文件加载成功")
        print(f"  - 家具类别数: {len(FURNITURE_CATEGORIES)}")
        print(f"  - 风格类别数: {len(FURNITURE_STYLES)}")
        
        # 测试模块导入
        from models.yolo_detector import FurnitureDetector
        print("✓ YOLO检测器模块导入成功")
        
        from models.style_classifier import StyleClassifier
        print("✓ 风格分类器模块导入成功")
        
        from models.material_classifier import MaterialClassifier
        print("✓ 材质分类器模块导入成功")
        
        from models.color_analyzer import ColorAnalyzer
        print("✓ 颜色分析器模块导入成功")
        
        from utils.image_processor import ImageProcessor
        print("✓ 图像处理器模块导入成功")
        
        from utils.result_formatter import ResultFormatter
        print("✓ 结果格式化器模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

def create_test_image():
    """创建测试图像"""
    try:
        import numpy as np
        import cv2
        
        # 创建简单的测试图像
        image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        cv2.rectangle(image, (100, 100), (500, 300), (139, 69, 19), -1)
        
        # 保存测试图像
        cv2.imwrite('test_image.jpg', image)
        print("✓ 测试图像创建成功: test_image.jpg")
        return True
        
    except Exception as e:
        print(f"✗ 测试图像创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("家具识别系统 - 环境检查")
    print("=" * 60)
    
    # 基础检查
    python_ok = test_python_version()
    basic_ok = test_basic_imports()
    
    # 依赖检查
    required_success, required_total = test_required_imports()
    optional_success, optional_total = test_optional_imports()
    
    # 项目结构检查
    structure_success, structure_total = test_project_structure()
    
    # 功能测试
    if required_success >= 3:  # 至少需要numpy, cv2, PIL
        yolo_ok = test_yolo_model()
        func_ok = test_basic_functionality()
        image_ok = create_test_image()
    else:
        print("\n⚠️  缺少必需依赖，跳过功能测试")
        yolo_ok = func_ok = image_ok = False
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    print(f"Python环境: {'✓' if python_ok else '✗'}")
    print(f"基础库: {'✓' if basic_ok else '✗'}")
    print(f"必需依赖: {required_success}/{required_total}")
    print(f"可选依赖: {optional_success}/{optional_total}")
    print(f"项目结构: {structure_success}/{structure_total}")
    
    if required_success >= 3:
        print(f"YOLO模型: {'✓' if yolo_ok else '✗'}")
        print(f"基本功能: {'✓' if func_ok else '✗'}")
        print(f"测试图像: {'✓' if image_ok else '✗'}")
    
    # 建议
    print("\n建议:")
    if required_success < required_total:
        print("- 请安装缺少的必需依赖，参考 INSTALL_GUIDE.md")
    if structure_success < structure_total:
        print("- 检查项目文件是否完整")
    if required_success >= 3 and not func_ok:
        print("- 检查模块导入错误，可能需要安装额外依赖")
    
    if required_success >= 3 and func_ok:
        print("✓ 系统基本就绪，可以尝试运行主程序")
        print("  运行命令: python main.py --help")
        print("  Web界面: streamlit run web_app.py")

if __name__ == "__main__":
    main()
