"""
中英文翻译映射配置
"""

# 家具类型翻译
FURNITURE_TYPES_CN = {
    'sofa': '沙发',
    'chair': '椅子',
    'table': '桌子',
    'bed': '床',
    'desk': '书桌',
    'cabinet': '柜子',
    'shelf': '架子',
    'dresser': '梳妆台',
    'nightstand': '床头柜',
    'wardrobe': '衣柜',
    'dining_table': '餐桌',
    'coffee_table': '茶几',
    'bookshelf': '书架',
    'tv_stand': '电视柜',
    'ottoman': '脚凳',
    'bench': '长凳',
    'stool': '凳子',
    'couch': '长沙发',
    'dining table': '餐桌',
    'tv': '电视',
    'laptop': '笔记本电脑',
    'mouse': '鼠标',
    'remote': '遥控器',
    'keyboard': '键盘',
    'cell phone': '手机',
    'microwave': '微波炉',
    'oven': '烤箱',
    'toaster': '烤面包机',
    'sink': '水槽',
    'refrigerator': '冰箱',
    'book': '书',
    'clock': '时钟',
    'vase': '花瓶',
    'scissors': '剪刀',
    'teddy bear': '泰迪熊',
    'hair drier': '吹风机',
    'toothbrush': '牙刷'
}

# 风格翻译
FURNITURE_STYLES_CN = {
    'modern': '现代风格',
    'contemporary': '当代风格',
    'traditional': '传统风格',
    'chinese': '中式风格',
    'american': '美式风格',
    'french': '法式风格',
    'vintage': '复古风格',
    'mid_century': '中世纪风格',
    'song_aesthetic': '宋式美学',
    'minimalist': '极简风格',
    'industrial': '工业风格',
    'scandinavian': '北欧风格',
    'rustic': '乡村风格',
    'baroque': '巴洛克风格',
    'art_deco': '装饰艺术风格',
    'classical': '古典风格',
    'neoclassical': '新古典风格',
    'mediterranean': '地中海风格',
    'japanese': '日式风格',
    'korean': '韩式风格'
}

# 材质翻译
FURNITURE_MATERIALS_CN = {
    'wood': '木材',
    'metal': '金属',
    'fabric': '布艺',
    'leather': '皮革',
    'plastic': '塑料',
    'glass': '玻璃',
    'rattan': '藤条',
    'bamboo': '竹子',
    'marble': '大理石',
    'stone': '石材',
    'composite': '复合材料',
    'velvet': '天鹅绒',
    'linen': '亚麻',
    'cotton': '棉布',
    'synthetic': '合成材料',
    'steel': '钢材',
    'aluminum': '铝材',
    'oak': '橡木',
    'pine': '松木',
    'mahogany': '红木',
    'teak': '柚木',
    'walnut': '胡桃木',
    'cherry': '樱桃木',
    'maple': '枫木',
    'birch': '桦木'
}

# 颜色翻译
COLOR_CATEGORIES_CN = {
    'white': '白色',
    'black': '黑色',
    'brown': '棕色',
    'gray': '灰色',
    'grey': '灰色',
    'beige': '米色',
    'cream': '奶油色',
    'red': '红色',
    'blue': '蓝色',
    'green': '绿色',
    'yellow': '黄色',
    'orange': '橙色',
    'purple': '紫色',
    'pink': '粉色',
    'gold': '金色',
    'silver': '银色',
    'navy': '深蓝色',
    'maroon': '栗色',
    'olive': '橄榄色',
    'lime': '青柠色',
    'aqua': '水蓝色',
    'teal': '青色',
    'fuchsia': '紫红色',
    'natural': '自然色',
    'earth_tones': '大地色调',
    'muted': '柔和色调'
}

# 使用场景翻译
USAGE_SCENARIOS_CN = {
    'living_room': '客厅',
    'bedroom': '卧室',
    'dining_room': '餐厅',
    'kitchen': '厨房',
    'office': '办公室',
    'study': '书房',
    'bathroom': '浴室',
    'outdoor': '户外',
    'entryway': '玄关',
    'children_room': '儿童房',
    'guest_room': '客房',
    'balcony': '阳台',
    'garden': '花园',
    'patio': '露台',
    'basement': '地下室',
    'attic': '阁楼',
    'hallway': '走廊',
    'closet': '衣帽间',
    'laundry_room': '洗衣房'
}

# 组合类型翻译
COMPOSITION_TYPES_CN = {
    'single_item': '单件家具',
    'mixed_furniture': '混合家具',
    'dining_set': '餐桌套装',
    'living_room_set': '客厅套装',
    'bedroom_set': '卧室套装',
    'office_set': '办公套装',
    'outdoor_set': '户外套装',
    'matched_set': '配套家具',
    'coordinated_pieces': '协调组合'
}

# 质量等级翻译
QUALITY_LEVELS_CN = {
    'excellent': '优秀',
    'good': '良好',
    'fair': '一般',
    'poor': '较差',
    'unknown': '未知'
}

# 和谐类型翻译
HARMONY_TYPES_CN = {
    'monochromatic': '单色调和',
    'analogous': '邻近色和谐',
    'complementary': '互补色和谐',
    'triadic': '三角色和谐',
    'split_complementary': '分裂互补色',
    'tetradic': '四角色和谐',
    'neutral': '中性色调',
    'warm': '暖色调',
    'cool': '冷色调',
    'mixed': '混合色调'
}

# 通用翻译函数
def translate_to_chinese(text, category='general'):
    """
    将英文文本翻译为中文
    
    Args:
        text: 要翻译的英文文本
        category: 翻译类别 ('furniture', 'style', 'material', 'color', 'usage', 'composition', 'quality', 'harmony')
    
    Returns:
        翻译后的中文文本
    """
    if not text or not isinstance(text, str):
        return text
    
    # 转换为小写进行匹配
    text_lower = text.lower().strip()
    
    # 根据类别选择翻译字典
    translation_maps = {
        'furniture': FURNITURE_TYPES_CN,
        'style': FURNITURE_STYLES_CN,
        'material': FURNITURE_MATERIALS_CN,
        'color': COLOR_CATEGORIES_CN,
        'usage': USAGE_SCENARIOS_CN,
        'composition': COMPOSITION_TYPES_CN,
        'quality': QUALITY_LEVELS_CN,
        'harmony': HARMONY_TYPES_CN
    }
    
    # 尝试从指定类别翻译
    if category in translation_maps:
        if text_lower in translation_maps[category]:
            return translation_maps[category][text_lower]
    
    # 如果指定类别没找到，尝试所有类别
    for trans_map in translation_maps.values():
        if text_lower in trans_map:
            return trans_map[text_lower]
    
    # 处理特殊格式
    if '_' in text:
        # 处理下划线分隔的文本
        parts = text.split('_')
        translated_parts = [translate_to_chinese(part, category) for part in parts]
        return ' '.join(translated_parts)
    
    # 如果没有找到翻译，返回原文本（首字母大写）
    return text.replace('_', ' ').title()

# 批量翻译函数
def translate_dict_values(data_dict, category_mapping=None):
    """
    批量翻译字典中的值
    
    Args:
        data_dict: 要翻译的字典
        category_mapping: 键到类别的映射
    
    Returns:
        翻译后的字典
    """
    if not isinstance(data_dict, dict):
        return data_dict
    
    translated = {}
    for key, value in data_dict.items():
        if category_mapping and key in category_mapping:
            category = category_mapping[key]
        else:
            category = 'general'
        
        if isinstance(value, str):
            translated[key] = translate_to_chinese(value, category)
        elif isinstance(value, dict):
            translated[key] = translate_dict_values(value, category_mapping)
        elif isinstance(value, list):
            translated[key] = [translate_to_chinese(item, category) if isinstance(item, str) else item for item in value]
        else:
            translated[key] = value
    
    return translated
