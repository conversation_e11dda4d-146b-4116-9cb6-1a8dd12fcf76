#!/usr/bin/env python3
"""
下载测试图片
"""
import requests
import os
from pathlib import Path

def download_test_images():
    """下载一些测试用的家具图片"""
    print("下载测试图片...")
    
    # 创建测试图片目录
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    # 一些公开的家具图片URL（来自免费图片网站）
    test_images = {
        "chair.jpg": "https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=640&h=480&fit=crop",
        "sofa.jpg": "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=640&h=480&fit=crop",
        "table.jpg": "https://images.unsplash.com/photo-1549497538-303791108f95?w=640&h=480&fit=crop"
    }
    
    downloaded = []
    
    for filename, url in test_images.items():
        filepath = test_dir / filename
        
        if filepath.exists():
            print(f"✓ {filename} 已存在")
            downloaded.append(str(filepath))
            continue
        
        try:
            print(f"下载 {filename}...")
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ {filename} 下载完成")
            downloaded.append(str(filepath))
            
        except Exception as e:
            print(f"❌ {filename} 下载失败: {e}")
    
    return downloaded

def test_with_downloaded_images():
    """使用下载的图片测试YOLO"""
    from ultralytics import YOLO
    
    model = YOLO('yolov8n.pt')
    
    # 下载测试图片
    image_paths = download_test_images()
    
    if not image_paths:
        print("没有可用的测试图片")
        return
    
    print("\n" + "=" * 50)
    print("使用真实图片测试YOLO检测")
    print("=" * 50)
    
    for image_path in image_paths:
        print(f"\n测试图片: {image_path}")
        
        try:
            results = model(image_path, conf=0.25, verbose=False)
            
            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    print(f"  检测到 {len(result.boxes)} 个对象:")
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        class_name = model.names[class_id]
                        confidence = float(box.conf[0])
                        print(f"    - {class_name}: {confidence:.3f}")
                else:
                    print("  没有检测到任何对象")
        
        except Exception as e:
            print(f"  检测失败: {e}")

if __name__ == "__main__":
    test_with_downloaded_images()
