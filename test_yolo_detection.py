#!/usr/bin/env python3
"""
测试YOLO检测功能
"""
import sys
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_yolo_basic():
    """测试基础YOLO功能"""
    print("=" * 50)
    print("测试YOLO基础功能")
    print("=" * 50)
    
    try:
        # 加载模型
        model = YOLO('yolov8n.pt')
        print("✓ YOLO模型加载成功")
        
        # 显示模型信息
        print(f"模型类别数量: {len(model.names)}")
        print("前20个类别:")
        for i, name in list(model.names.items())[:20]:
            print(f"  {i}: {name}")
        
        return model
    except Exception as e:
        print(f"❌ YOLO模型加载失败: {e}")
        return None

def test_detection_with_sample():
    """使用示例图片测试检测"""
    print("\n" + "=" * 50)
    print("测试图片检测")
    print("=" * 50)
    
    model = YOLO('yolov8n.pt')
    
    # 创建一个简单的测试图片
    test_image = np.ones((640, 640, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 在图片上画一个简单的矩形（模拟家具）
    cv2.rectangle(test_image, (100, 100), (300, 200), (0, 0, 255), -1)  # 红色矩形
    cv2.rectangle(test_image, (400, 300), (600, 500), (0, 255, 0), -1)  # 绿色矩形
    
    # 保存测试图片
    test_image_path = "test_image.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✓ 测试图片已创建: {test_image_path}")
    
    # 运行检测
    try:
        results = model(test_image_path, conf=0.1, verbose=True)  # 降低置信度
        
        print(f"检测结果数量: {len(results)}")
        
        for i, result in enumerate(results):
            print(f"\n结果 {i+1}:")
            if result.boxes is not None:
                print(f"  检测到的对象数量: {len(result.boxes)}")
                for j, box in enumerate(result.boxes):
                    class_id = int(box.cls[0])
                    class_name = model.names[class_id]
                    confidence = float(box.conf[0])
                    print(f"    对象 {j+1}: {class_name} (置信度: {confidence:.3f})")
            else:
                print("  没有检测到任何对象")
        
        return True
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return False

def test_furniture_detector():
    """测试我们的家具检测器"""
    print("\n" + "=" * 50)
    print("测试家具检测器")
    print("=" * 50)
    
    try:
        from models.yolo_detector import FurnitureDetector
        
        detector = FurnitureDetector()
        print("✓ 家具检测器初始化成功")
        
        # 测试检测
        test_image_path = "test_image.jpg"
        detections = detector.detect_furniture(test_image_path)
        
        print(f"家具检测结果: {len(detections)} 个")
        for detection in detections:
            print(f"  - {detection['class_name']}: {detection['confidence']:.3f}")
        
        return True
    except Exception as e:
        print(f"❌ 家具检测器测试失败: {e}")
        return False

def test_with_real_furniture_image():
    """使用真实家具图片测试"""
    print("\n" + "=" * 50)
    print("创建真实家具测试图片")
    print("=" * 50)
    
    # 创建一个更像家具的测试图片
    img = np.ones((480, 640, 3), dtype=np.uint8) * 240  # 浅灰色背景
    
    # 画一个椅子的简单轮廓
    # 椅背
    cv2.rectangle(img, (200, 100), (220, 300), (139, 69, 19), -1)  # 棕色
    # 座椅
    cv2.rectangle(img, (180, 280), (320, 300), (139, 69, 19), -1)
    # 椅腿
    cv2.rectangle(img, (185, 300), (195, 400), (139, 69, 19), -1)
    cv2.rectangle(img, (205, 300), (215, 400), (139, 69, 19), -1)
    cv2.rectangle(img, (285, 300), (295, 400), (139, 69, 19), -1)
    cv2.rectangle(img, (305, 300), (315, 400), (139, 69, 19), -1)
    
    # 画一个桌子
    # 桌面
    cv2.rectangle(img, (400, 250), (600, 270), (160, 82, 45), -1)  # 棕色桌面
    # 桌腿
    cv2.rectangle(img, (410, 270), (420, 400), (160, 82, 45), -1)
    cv2.rectangle(img, (590, 270), (600, 400), (160, 82, 45), -1)
    cv2.rectangle(img, (410, 270), (420, 400), (160, 82, 45), -1)
    cv2.rectangle(img, (590, 270), (600, 400), (160, 82, 45), -1)
    
    furniture_image_path = "furniture_test.jpg"
    cv2.imwrite(furniture_image_path, img)
    print(f"✓ 家具测试图片已创建: {furniture_image_path}")
    
    # 测试检测
    model = YOLO('yolov8n.pt')
    results = model(furniture_image_path, conf=0.1, verbose=True)
    
    print("检测结果:")
    for result in results:
        if result.boxes is not None:
            print(f"检测到 {len(result.boxes)} 个对象")
            for box in result.boxes:
                class_id = int(box.cls[0])
                class_name = model.names[class_id]
                confidence = float(box.conf[0])
                print(f"  - {class_name}: {confidence:.3f}")
        else:
            print("没有检测到任何对象")

def main():
    """主函数"""
    print("YOLO检测功能诊断工具")
    
    # 测试基础功能
    model = test_yolo_basic()
    if not model:
        return
    
    # 测试检测
    test_detection_with_sample()
    
    # 测试家具检测器
    test_furniture_detector()
    
    # 测试真实家具图片
    test_with_real_furniture_image()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
