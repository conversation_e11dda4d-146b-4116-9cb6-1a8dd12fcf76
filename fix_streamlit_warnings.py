#!/usr/bin/env python3
"""
修复Streamlit警告和兼容性问题
"""
import os
import sys
import warnings

def suppress_warnings():
    """抑制不必要的警告"""
    # 抑制PyTorch相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="torchvision")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")
    
    # 抑制Streamlit相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="streamlit")
    
    # 设置环境变量来减少警告
    os.environ["PYTHONWARNINGS"] = "ignore"
    
    print("✓ 警告抑制设置已应用")

def check_streamlit_config():
    """检查和创建Streamlit配置"""
    config_dir = os.path.expanduser("~/.streamlit")
    config_file = os.path.join(config_dir, "config.toml")
    
    # 创建配置目录
    os.makedirs(config_dir, exist_ok=True)
    
    # 创建配置文件
    config_content = """
[global]
developmentMode = false
showWarningOnDirectExecution = false

[server]
headless = true
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[logger]
level = "error"
"""
    
    with open(config_file, "w", encoding="utf-8") as f:
        f.write(config_content.strip())
    
    print(f"✓ Streamlit配置文件已创建: {config_file}")

def main():
    """主函数"""
    print("=" * 50)
    print("修复Streamlit警告和兼容性问题")
    print("=" * 50)
    
    suppress_warnings()
    check_streamlit_config()
    
    print("\n✅ 修复完成！")
    print("现在可以重新启动Web应用，警告信息会大幅减少。")

if __name__ == "__main__":
    main()
