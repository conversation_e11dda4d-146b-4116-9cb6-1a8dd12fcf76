"""
Streamlit web application for furniture analysis
"""
import streamlit as st
import os
import sys
from pathlib import Path
import tempfile
import json
from PIL import Image
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from main import FurnitureAnalyzer


def main():
    """Main Streamlit application"""
    st.set_page_config(
        page_title="家具识别分析系统",
        page_icon="🪑",
        layout="wide"
    )

    st.title("🪑 家具识别分析系统")
    st.markdown("使用YOLO和深度学习技术识别家具类型、风格、材质、颜色等属性")

    # Initialize analyzer (with caching)
    @st.cache_resource
    def load_analyzer():
        return FurnitureAnalyzer()

    try:
        analyzer = load_analyzer()
    except Exception as e:
        st.error(f"系统初始化失败: {e}")
        st.stop()

    # Sidebar for options
    st.sidebar.header("分析选项")

    # File upload
    uploaded_file = st.file_uploader(
        "选择家具图片",
        type=['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp'],
        help="支持常见图片格式"
    )

    # Analysis options
    show_detailed_results = st.sidebar.checkbox("显示详细分析结果", value=True)
    show_confidence_scores = st.sidebar.checkbox("显示置信度分数", value=True)

    if uploaded_file is not None:
        # Display uploaded image
        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("上传的图片")
            image = Image.open(uploaded_file)
            st.image(image, caption="待分析图片", use_container_width=True)

        # Analyze button
        if st.button("开始分析", type="primary"):
            with st.spinner("正在分析图片，请稍候..."):
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
                    image.save(tmp_file.name)
                    temp_path = tmp_file.name

                try:
                    # Analyze image
                    result = analyzer.analyze_image(temp_path)

                    # Check if any furniture was detected
                    if 'error' in result:
                        st.error(f"分析失败: {result['error']}")
                    elif result.get('summary', {}).get('total_furniture_items', 0) == 0:
                        # No furniture detected - show helpful message
                        with col2:
                            st.warning("🔍 未检测到家具")
                            st.info("""
                            **可能的原因：**
                            - 图片中没有明显的家具
                            - 图片质量较低或模糊
                            - 家具被遮挡或角度不佳

                            **建议：**
                            - 确保图片清晰且光线充足
                            - 尝试不同角度的家具照片
                            - 确保家具在图片中占据主要位置
                            """)

                            # Show sample images
                            st.subheader("📸 参考示例")
                            sample_images = ["test_images/chair.jpg", "test_images/sofa.jpg", "test_images/table.jpg"]
                            cols = st.columns(3)
                            for i, img_path in enumerate(sample_images):
                                if Path(img_path).exists():
                                    with cols[i]:
                                        st.image(img_path, caption=f"示例 {i+1}", use_container_width=True)
                    else:
                        # Display results
                        with col2:
                            display_analysis_results(result, show_detailed_results, show_confidence_scores)

                        # Display detailed breakdown
                        if show_detailed_results:
                            display_detailed_breakdown(result)

                        # Download results
                        st.subheader("下载分析结果")

                        # JSON download - 转换numpy类型为Python原生类型
                        def convert_numpy_types(obj):
                            """递归转换numpy类型为Python原生类型"""
                            if isinstance(obj, dict):
                                return {key: convert_numpy_types(value) for key, value in obj.items()}
                            elif isinstance(obj, list):
                                return [convert_numpy_types(item) for item in obj]
                            elif hasattr(obj, 'item'):  # numpy scalar
                                return obj.item()
                            elif hasattr(obj, 'tolist'):  # numpy array
                                return obj.tolist()
                            else:
                                return obj

                        result_serializable = convert_numpy_types(result)
                        json_str = json.dumps(result_serializable, ensure_ascii=False, indent=2)
                        st.download_button(
                            label="下载JSON格式结果",
                            data=json_str,
                            file_name="furniture_analysis.json",
                            mime="application/json"
                        )

                        # Text report download
                        text_report = analyzer.result_formatter.create_readable_report(result)
                        st.download_button(
                            label="下载文本报告",
                            data=text_report,
                            file_name="furniture_report.txt",
                            mime="text/plain"
                        )

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)

    else:
        # Show example or instructions
        st.info("请上传一张家具图片开始分析")

        # Show example results
        st.subheader("功能介绍")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            **🔍 家具检测**
            - 识别图片中的家具类型
            - 检测多个家具对象
            - 分析家具组合关系
            """)

        with col2:
            st.markdown("""
            **🎨 风格分析**
            - 现代风格
            - 中式风格
            - 美式风格
            - 法式风格
            - 中古风格
            - 宋式美学
            """)

        with col3:
            st.markdown("""
            **📊 属性识别**
            - 材质分析（木材、金属、布艺等）
            - 颜色提取和分析
            - 使用场景判断
            - 质量评估
            """)


def display_analysis_results(result, show_detailed=True, show_confidence=True):
    """Display analysis results in the main area"""
    if 'error' in result:
        st.error(f"分析失败: {result['error']}")
        return

    summary = result.get('summary', {})

    st.subheader("分析结果")

    # Main metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "检测到的家具数量",
            summary.get('total_furniture_items', 0)
        )

    with col2:
        is_set = summary.get('is_furniture_set', False)
        st.metric(
            "是否为家具组合",
            "是" if is_set else "否"
        )

    with col3:
        if is_set:
            st.metric(
                "组合类型",
                summary.get('set_type', '未知').replace('_', ' ').title()
            )

    # Key characteristics
    st.subheader("主要特征")

    characteristics = {
        "主导风格": summary.get('dominant_style', '未知'),
        "主要材质": summary.get('dominant_material', '未知'),
        "主要颜色": summary.get('dominant_color', '未知'),
        "使用场景": summary.get('primary_usage_scenario', '未知').replace('_', ' ').title()
    }

    for key, value in characteristics.items():
        st.write(f"**{key}**: {value}")


def display_detailed_breakdown(result):
    """Display detailed breakdown of each furniture item"""
    furniture_details = result.get('furniture_details', [])

    if not furniture_details:
        return

    st.subheader("详细分析")

    for i, item in enumerate(furniture_details, 1):
        with st.expander(f"家具 {i}: {item.get('furniture_type', '未知').title()}"):

            col1, col2 = st.columns(2)

            with col1:
                st.write("**基本信息**")
                st.write(f"类型: {item.get('furniture_type', '未知')}")
                st.write(f"检测置信度: {item.get('detection_confidence', 0):.2f}")
                st.write(f"使用场景: {item.get('usage_scenario', '未知')}")

                # Bounding box info
                bbox = item.get('bounding_box', [])
                if bbox:
                    st.write(f"位置: ({bbox[0]}, {bbox[1]}) - ({bbox[2]}, {bbox[3]})")

            with col2:
                st.write("**属性分析**")

                # Style
                style_info = item.get('style', {})
                if style_info:
                    style = style_info.get('predicted_style', '未知')
                    confidence = style_info.get('confidence', 0)
                    st.write(f"风格: {style} (置信度: {confidence:.2f})")

                # Material
                material_info = item.get('material', {})
                if material_info:
                    material = material_info.get('predicted_material', '未知')
                    confidence = material_info.get('confidence', 0)
                    st.write(f"材质: {material} (置信度: {confidence:.2f})")

                # Color
                color_info = item.get('colors', {})
                if color_info:
                    primary_color = color_info.get('primary_color', '未知')
                    st.write(f"主要颜色: {primary_color}")

                    dominant_colors = color_info.get('dominant_colors', [])
                    if dominant_colors:
                        st.write(f"色彩组合: {', '.join(dominant_colors[:3])}")

            # Additional details
            if st.checkbox(f"显示更多详情 - 家具 {i}", key=f"details_{i}"):

                # Style predictions
                style_info = item.get('style', {})
                if style_info and 'all_predictions' in style_info:
                    st.write("**所有风格预测**")
                    style_df = pd.DataFrame([
                        {"风格": style, "置信度": f"{conf:.3f}"}
                        for style, conf in sorted(
                            style_info['all_predictions'].items(),
                            key=lambda x: x[1], reverse=True
                        )[:5]
                    ])
                    st.dataframe(style_df, use_container_width=True)

                # Material predictions
                material_info = item.get('material', {})
                if material_info and 'all_predictions' in material_info:
                    st.write("**所有材质预测**")
                    material_df = pd.DataFrame([
                        {"材质": material, "置信度": f"{conf:.3f}"}
                        for material, conf in sorted(
                            material_info['all_predictions'].items(),
                            key=lambda x: x[1], reverse=True
                        )[:5]
                    ])
                    st.dataframe(material_df, use_container_width=True)

                # Color harmony
                color_info = item.get('colors', {})
                if color_info and 'color_harmony' in color_info:
                    harmony = color_info['color_harmony']
                    st.write("**色彩和谐度分析**")
                    st.write(f"和谐类型: {harmony.get('harmony_type', '未知')}")
                    st.write(f"和谐分数: {harmony.get('harmony_score', 0):.2f}")


if __name__ == "__main__":
    main()
