"""
Main application for furniture recognition and analysis
"""
import os
import sys
import argparse
from pathlib import Path
from typing import List, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.yolo_detector import FurnitureDetector
from models.style_classifier import StyleClassifier
from models.material_classifier import MaterialClassifier
from models.color_analyzer import ColorAnalyzer
from utils.image_processor import ImageProcessor
from utils.result_formatter import ResultFormatter


class FurnitureAnalyzer:
    """Main furniture analysis system"""

    def __init__(self):
        """Initialize the furniture analyzer"""
        print("Initializing Furniture Analyzer...")

        # Initialize components
        self.detector = FurnitureDetector()
        self.style_classifier = StyleClassifier()
        self.material_classifier = MaterialClassifier()
        self.color_analyzer = ColorAnalyzer()
        self.image_processor = ImageProcessor()
        self.result_formatter = ResultFormatter(use_chinese=True)

        print("Furniture Analyzer initialized successfully!")

    def analyze_image(self, image_path: str, output_dir: Optional[str] = None) -> Dict:
        """
        Analyze a single furniture image

        Args:
            image_path: Path to the image file
            output_dir: Optional output directory for results

        Returns:
            Analysis results dictionary
        """
        print(f"\nAnalyzing image: {image_path}")

        # Load and preprocess image
        image = self.image_processor.load_and_preprocess(image_path)
        if image is None:
            return {'error': 'Failed to load image'}

        # Detect furniture
        print("Detecting furniture...")
        detections = self.detector.detect_furniture(image_path)

        if not detections:
            print("No furniture detected in the image.")
            return {
                'image_path': image_path,
                'detections': [],
                'message': 'No furniture detected'
            }

        print(f"Found {len(detections)} furniture item(s)")

        # Extract furniture regions for detailed analysis
        furniture_regions = self.detector.extract_furniture_regions(image_path)

        # Analyze each furniture item
        style_results = []
        material_results = []
        color_results = []

        for i, (region_image, detection) in enumerate(furniture_regions):
            print(f"Analyzing furniture item {i+1}/{len(furniture_regions)}...")

            # Style classification
            style_result = self.style_classifier.classify_style(region_image)
            style_results.append(style_result)

            # Material classification
            material_result = self.material_classifier.classify_material(region_image)
            material_results.append(material_result)

            # Color analysis
            color_result = self.color_analyzer.analyze_colors(region_image)
            color_results.append(color_result)

        # Analyze furniture composition
        print("Analyzing furniture composition...")
        composition_analysis = self.detector.analyze_furniture_composition(detections)

        # Format results
        formatted_result = self.result_formatter.format_analysis_result(
            image_path=image_path,
            detections=detections,
            style_results=style_results,
            material_results=material_results,
            color_results=color_results,
            composition_analysis=composition_analysis
        )

        # Save results if output directory is specified
        if output_dir:
            self._save_results(formatted_result, output_dir)

        return formatted_result

    def analyze_batch(self, image_dir: str, output_dir: str) -> List[Dict]:
        """
        Analyze multiple images in a directory

        Args:
            image_dir: Directory containing images
            output_dir: Output directory for results

        Returns:
            List of analysis results
        """
        print(f"\nAnalyzing images in directory: {image_dir}")

        # Find image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        image_files = []

        for ext in image_extensions:
            image_files.extend(Path(image_dir).glob(f'*{ext}'))
            image_files.extend(Path(image_dir).glob(f'*{ext.upper()}'))

        if not image_files:
            print("No image files found in the directory.")
            return []

        print(f"Found {len(image_files)} image(s) to analyze")

        # Analyze each image
        results = []
        for i, image_path in enumerate(image_files, 1):
            print(f"\n--- Processing image {i}/{len(image_files)} ---")
            result = self.analyze_image(str(image_path), output_dir)
            results.append(result)

        # Create batch summary
        self._create_batch_summary(results, output_dir)

        return results

    def _save_results(self, result: Dict, output_dir: str):
        """Save analysis results to files"""
        os.makedirs(output_dir, exist_ok=True)

        # Get image filename without extension
        image_name = Path(result['image_info']['filename']).stem

        # Save JSON result
        json_path = os.path.join(output_dir, f"{image_name}_analysis.json")
        self.result_formatter.export_to_json(result, json_path)

        # Save readable report
        report_path = os.path.join(output_dir, f"{image_name}_report.txt")
        self.result_formatter.export_readable_report(result, report_path)

        print(f"Results saved to: {output_dir}")

    def _create_batch_summary(self, results: List[Dict], output_dir: str):
        """Create a summary of batch analysis results"""
        summary = {
            'total_images': len(results),
            'successful_analyses': len([r for r in results if 'error' not in r]),
            'total_furniture_items': sum(r.get('summary', {}).get('total_furniture_items', 0) for r in results),
            'style_distribution': {},
            'material_distribution': {},
            'color_distribution': {},
            'furniture_type_distribution': {}
        }

        # Aggregate statistics
        for result in results:
            if 'error' in result:
                continue

            result_summary = result.get('summary', {})

            # Aggregate distributions
            for dist_type in ['style_distribution', 'material_distribution',
                            'color_distribution', 'furniture_type_distribution']:
                dist = result_summary.get(dist_type, {})
                for key, value in dist.items():
                    summary[dist_type][key] = summary[dist_type].get(key, 0) + value

        # Save batch summary
        summary_path = os.path.join(output_dir, "batch_summary.json")
        self.result_formatter.export_to_json(summary, summary_path)

        print(f"Batch summary saved to: {summary_path}")

    def print_result_summary(self, result: Dict):
        """Print a summary of analysis results"""
        if 'error' in result:
            print(f"Error: {result['error']}")
            return

        summary = result.get('summary', {})
        print("\n" + "="*50)
        print("ANALYSIS SUMMARY")
        print("="*50)
        print(f"Image: {result['image_info']['filename']}")
        print(f"Total furniture items: {summary.get('total_furniture_items', 0)}")
        print(f"Is furniture set: {'Yes' if summary.get('is_furniture_set', False) else 'No'}")

        if summary.get('is_furniture_set', False):
            print(f"Set type: {summary.get('set_type', 'Unknown')}")

        print(f"Dominant style: {summary.get('dominant_style', 'Unknown')}")
        print(f"Dominant material: {summary.get('dominant_material', 'Unknown')}")
        print(f"Dominant color: {summary.get('dominant_color', 'Unknown')}")
        print(f"Primary usage scenario: {summary.get('primary_usage_scenario', 'Unknown')}")

        # Print individual items
        furniture_details = result.get('furniture_details', [])
        if furniture_details:
            print("\nDetailed Analysis:")
            for i, item in enumerate(furniture_details, 1):
                print(f"\nItem {i}: {item.get('furniture_type', 'Unknown')}")
                style = item.get('style', {}).get('predicted_style', 'Unknown')
                material = item.get('material', {}).get('predicted_material', 'Unknown')
                color = item.get('colors', {}).get('primary_color', 'Unknown')
                scenario = item.get('usage_scenario', 'Unknown')

                print(f"  Style: {style}")
                print(f"  Material: {material}")
                print(f"  Color: {color}")
                print(f"  Usage: {scenario}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Furniture Recognition and Analysis System')
    parser.add_argument('input', help='Input image file or directory')
    parser.add_argument('-o', '--output', help='Output directory for results')
    parser.add_argument('-b', '--batch', action='store_true',
                       help='Process all images in input directory')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Verbose output')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = FurnitureAnalyzer()

    try:
        if args.batch:
            # Batch processing
            if not os.path.isdir(args.input):
                print("Error: Input must be a directory for batch processing")
                return

            output_dir = args.output or "output"
            results = analyzer.analyze_batch(args.input, output_dir)

            print(f"\nBatch processing completed. Analyzed {len(results)} images.")

        else:
            # Single image processing
            if not os.path.isfile(args.input):
                print("Error: Input file does not exist")
                return

            result = analyzer.analyze_image(args.input, args.output)

            # Print summary
            analyzer.print_result_summary(result)

    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
    except Exception as e:
        print(f"Error during analysis: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
