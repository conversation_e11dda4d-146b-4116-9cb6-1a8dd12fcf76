"""
Test script for furniture detection and analysis
"""
import sys
import os
from pathlib import Path
import numpy as np
import cv2

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from models.yolo_detector import FurnitureDetector
from models.style_classifier import StyleClassifier
from models.material_classifier import MaterialClassifier
from models.color_analyzer import ColorAnalyzer
from utils.image_processor import ImageProcessor


def create_test_image():
    """Create a simple test image for testing"""
    # Create a simple test image (brown rectangle representing furniture)
    test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255  # White background
    
    # Add a brown rectangle (simulating wooden furniture)
    cv2.rectangle(test_image, (100, 100), (500, 300), (139, 69, 19), -1)  # Brown rectangle
    
    # Add some texture lines
    for i in range(110, 290, 20):
        cv2.line(test_image, (110, i), (490, i), (120, 60, 15), 2)
    
    return test_image


def test_image_processor():
    """Test image processor functionality"""
    print("Testing Image Processor...")
    
    processor = ImageProcessor()
    
    # Create test image
    test_image = create_test_image()
    
    # Test preprocessing
    processed = processor.preprocess_image(test_image)
    print(f"Original shape: {test_image.shape}, Processed shape: {processed.shape}")
    
    # Test quality score
    quality_score = processor.calculate_image_quality_score(test_image)
    print(f"Image quality score: {quality_score:.3f}")
    
    print("Image Processor test completed.\n")


def test_color_analyzer():
    """Test color analyzer functionality"""
    print("Testing Color Analyzer...")
    
    analyzer = ColorAnalyzer()
    
    # Create test image
    test_image = create_test_image()
    
    # Analyze colors
    color_result = analyzer.analyze_colors(test_image)
    
    print(f"Primary color: {color_result.get('primary_color', 'Unknown')}")
    print(f"Dominant colors: {color_result.get('dominant_colors_names', [])}")
    print(f"Brightness level: {color_result.get('brightness_level', 'Unknown')}")
    print(f"Color harmony: {color_result.get('color_harmony', {}).get('harmony_type', 'Unknown')}")
    
    print("Color Analyzer test completed.\n")


def test_material_classifier():
    """Test material classifier functionality"""
    print("Testing Material Classifier...")
    
    classifier = MaterialClassifier()
    
    # Create test image
    test_image = create_test_image()
    
    # Classify material
    material_result = classifier.classify_material(test_image)
    
    print(f"Predicted material: {material_result.get('predicted_material', 'Unknown')}")
    print(f"Confidence: {material_result.get('confidence', 0):.3f}")
    print(f"Top 3 predictions:")
    
    all_predictions = material_result.get('all_predictions', {})
    sorted_predictions = sorted(all_predictions.items(), key=lambda x: x[1], reverse=True)
    
    for material, confidence in sorted_predictions[:3]:
        print(f"  {material}: {confidence:.3f}")
    
    print("Material Classifier test completed.\n")


def test_style_classifier():
    """Test style classifier functionality"""
    print("Testing Style Classifier...")
    
    classifier = StyleClassifier()
    
    # Create test image
    test_image = create_test_image()
    
    # Classify style
    style_result = classifier.classify_style(test_image)
    
    print(f"Predicted style: {style_result.get('predicted_style', 'Unknown')}")
    print(f"Confidence: {style_result.get('confidence', 0):.3f}")
    print(f"Classification method: {style_result.get('method', 'Unknown')}")
    
    print("Style Classifier test completed.\n")


def test_furniture_detector():
    """Test furniture detector functionality"""
    print("Testing Furniture Detector...")
    
    detector = FurnitureDetector()
    
    # Create a test image file
    test_image = create_test_image()
    test_image_path = "test_furniture.jpg"
    
    # Save test image
    cv2.imwrite(test_image_path, test_image)
    
    try:
        # Test detection (Note: This will use a general YOLO model, 
        # so it might not detect our simple test image as furniture)
        detections = detector.detect_furniture(test_image_path)
        
        print(f"Number of detections: {len(detections)}")
        
        for i, detection in enumerate(detections):
            print(f"Detection {i+1}:")
            print(f"  Class: {detection.get('class_name', 'Unknown')}")
            print(f"  Confidence: {detection.get('confidence', 0):.3f}")
            print(f"  Bounding box: {detection.get('bbox', [])}")
        
        # Test composition analysis
        composition = detector.analyze_furniture_composition(detections)
        print(f"Composition analysis: {composition}")
        
    finally:
        # Clean up test file
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
    
    print("Furniture Detector test completed.\n")


def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("FURNITURE ANALYSIS SYSTEM - COMPONENT TESTS")
    print("=" * 60)
    
    try:
        test_image_processor()
        test_color_analyzer()
        test_material_classifier()
        test_style_classifier()
        test_furniture_detector()
        
        print("=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
