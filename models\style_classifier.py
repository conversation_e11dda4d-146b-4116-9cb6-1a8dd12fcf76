"""
Furniture style classification module
"""
import cv2
import numpy as np
import torch
import torch.nn as nn
from torchvision import transforms, models
from typing import Dict, List, Tuple
from PIL import Image

from config.settings import FURNITURE_STYLES, STYLE_CONFIDENCE_THRESHOLD


class StyleClassifier:
    """Furniture style classifier using CNN"""

    def __init__(self):
        """Initialize the style classifier"""
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.styles = FURNITURE_STYLES
        self.confidence_threshold = STYLE_CONFIDENCE_THRESHOLD

        # Initialize model (using ResNet as base)
        self.model = self._create_model()
        self.transform = self._get_transforms()

        # Style characteristics for rule-based classification
        self.style_features = {
            'modern': {
                'colors': ['white', 'black', 'gray'],
                'shapes': ['clean_lines', 'geometric'],
                'materials': ['metal', 'glass', 'plastic']
            },
            'chinese': {
                'colors': ['red', 'gold', 'brown'],
                'shapes': ['curved', 'ornate'],
                'materials': ['wood', 'bamboo']
            },
            'american': {
                'colors': ['brown', 'beige', 'cream'],
                'shapes': ['sturdy', 'practical'],
                'materials': ['wood', 'leather']
            },
            'french': {
                'colors': ['cream', 'gold', 'white'],
                'shapes': ['elegant', 'curved'],
                'materials': ['wood', 'fabric']
            },
            'vintage': {
                'colors': ['brown', 'cream', 'muted'],
                'shapes': ['classic', 'worn'],
                'materials': ['wood', 'metal']
            },
            'song_aesthetic': {
                'colors': ['natural', 'earth_tones'],
                'shapes': ['simple', 'elegant'],
                'materials': ['wood', 'bamboo']
            }
        }

    def _create_model(self) -> nn.Module:
        """Create the style classification model"""
        # Use pre-trained ResNet18 as base
        model = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)

        # Modify the final layer for style classification
        num_features = model.fc.in_features
        model.fc = nn.Linear(num_features, len(self.styles))

        model = model.to(self.device)
        model.eval()

        return model

    def _get_transforms(self):
        """Get image preprocessing transforms"""
        return transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

    def classify_style(self, image: np.ndarray) -> Dict:
        """
        Classify furniture style from image

        Args:
            image: Input image as numpy array (BGR format)

        Returns:
            Dictionary with style predictions and confidence scores
        """
        try:
            # Convert BGR to RGB
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image

            # Convert to PIL Image
            pil_image = Image.fromarray(image_rgb)

            # Apply transforms
            input_tensor = self.transform(pil_image).unsqueeze(0).to(self.device)

            # Get model predictions
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)

            # Convert to numpy
            probs = probabilities.cpu().numpy()[0]

            # Create results dictionary
            style_predictions = {}
            for i, style in enumerate(self.styles):
                style_predictions[style] = float(probs[i])

            # Get top prediction
            top_style = max(style_predictions.items(), key=lambda x: x[1])

            # Apply rule-based refinement
            refined_prediction = self._refine_with_rules(image_rgb, style_predictions)

            return {
                'predicted_style': refined_prediction['style'],
                'confidence': refined_prediction['confidence'],
                'all_predictions': style_predictions,
                'method': 'cnn_with_rules'
            }

        except Exception as e:
            print(f"Error in style classification: {e}")
            return self._fallback_style_classification(image)

    def _refine_with_rules(self, image: np.ndarray, cnn_predictions: Dict) -> Dict:
        """
        Refine CNN predictions using rule-based analysis

        Args:
            image: Input image
            cnn_predictions: CNN model predictions

        Returns:
            Refined prediction
        """
        # Analyze image features
        color_analysis = self._analyze_colors(image)
        shape_analysis = self._analyze_shapes(image)

        # Calculate rule-based scores
        rule_scores = {}

        for style, features in self.style_features.items():
            score = 0.0

            # Color matching
            for color in features['colors']:
                if color in color_analysis['dominant_colors']:
                    score += 0.3

            # Combine with CNN prediction
            cnn_score = cnn_predictions.get(style, 0.0)
            final_score = 0.7 * cnn_score + 0.3 * score

            rule_scores[style] = final_score

        # Get best prediction
        best_style = max(rule_scores.items(), key=lambda x: x[1])

        return {
            'style': best_style[0],
            'confidence': best_style[1]
        }

    def _analyze_colors(self, image: np.ndarray) -> Dict:
        """Analyze dominant colors in the image"""
        # Resize image for faster processing
        small_image = cv2.resize(image, (100, 100))

        # Convert to HSV for better color analysis
        hsv = cv2.cvtColor(small_image, cv2.COLOR_RGB2HSV)

        # Simple color categorization based on HSV values
        dominant_colors = []

        # Calculate average hue
        avg_hue = np.mean(hsv[:, :, 0])
        avg_saturation = np.mean(hsv[:, :, 1])
        avg_value = np.mean(hsv[:, :, 2])

        # Categorize colors
        if avg_saturation < 50:  # Low saturation
            if avg_value > 200:
                dominant_colors.append('white')
            elif avg_value < 50:
                dominant_colors.append('black')
            else:
                dominant_colors.append('gray')
        else:  # High saturation
            if 0 <= avg_hue < 15 or 165 <= avg_hue <= 180:
                dominant_colors.append('red')
            elif 15 <= avg_hue < 45:
                dominant_colors.append('orange')
            elif 45 <= avg_hue < 75:
                dominant_colors.append('yellow')
            elif 75 <= avg_hue < 105:
                dominant_colors.append('green')
            elif 105 <= avg_hue < 135:
                dominant_colors.append('blue')
            else:
                dominant_colors.append('purple')

        return {
            'dominant_colors': dominant_colors,
            'avg_hue': avg_hue,
            'avg_saturation': avg_saturation,
            'avg_value': avg_value
        }

    def _analyze_shapes(self, image: np.ndarray) -> Dict:
        """Analyze shapes and lines in the image"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

        # Edge detection
        edges = cv2.Canny(gray, 50, 150)

        # Line detection
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                               minLineLength=30, maxLineGap=10)

        shape_features = {
            'has_straight_lines': False,
            'has_curves': False,
            'line_count': 0
        }

        if lines is not None:
            shape_features['line_count'] = len(lines)
            shape_features['has_straight_lines'] = len(lines) > 10

        return shape_features

    def _fallback_style_classification(self, image: np.ndarray) -> Dict:
        """Fallback classification using simple rules"""
        color_analysis = self._analyze_colors(image)

        # Simple rule-based classification
        if 'white' in color_analysis['dominant_colors'] or 'gray' in color_analysis['dominant_colors']:
            return {
                'predicted_style': 'modern',
                'confidence': 0.6,
                'all_predictions': {'modern': 0.6},
                'method': 'fallback_rules'
            }
        elif 'brown' in color_analysis['dominant_colors']:
            return {
                'predicted_style': 'traditional',
                'confidence': 0.5,
                'all_predictions': {'traditional': 0.5},
                'method': 'fallback_rules'
            }
        else:
            return {
                'predicted_style': 'contemporary',
                'confidence': 0.4,
                'all_predictions': {'contemporary': 0.4},
                'method': 'fallback_rules'
            }
