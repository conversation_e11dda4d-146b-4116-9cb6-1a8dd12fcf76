#!/usr/bin/env python3
"""
增强版家具分析器 - 提供更详细和准确的家具识别
"""
import sys
from pathlib import Path
import cv2
import numpy as np
from typing import Dict, List, Tuple
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.yolo_detector import FurnitureDetector
from models.style_classifier import StyleClassifier
from models.material_classifier import MaterialClassifier
from models.color_analyzer import ColorAnalyzer
from utils.image_processor import ImageProcessor
from utils.result_formatter import ResultFormatter

class EnhancedFurnitureAnalyzer:
    """增强版家具分析器"""
    
    def __init__(self):
        """初始化增强版分析器"""
        print("初始化增强版家具分析器...")
        
        # 基础组件
        self.detector = FurnitureDetector()
        self.style_classifier = StyleClassifier()
        self.material_classifier = MaterialClassifier()
        self.color_analyzer = ColorAnalyzer()
        self.image_processor = ImageProcessor()
        self.result_formatter = ResultFormatter(use_chinese=True)
        
        # 增强的家具特征数据库
        self.furniture_database = self._load_furniture_database()
        
        print("增强版家具分析器初始化完成！")
    
    def _load_furniture_database(self) -> Dict:
        """加载增强的家具特征数据库"""
        return {
            "沙发": {
                "风格特征": {
                    "现代简约": {
                        "关键词": ["简洁", "线条", "几何", "功能性"],
                        "颜色": ["白色", "灰色", "黑色", "米色"],
                        "材质": ["皮革", "布艺", "金属"],
                        "形状": ["直线型", "L型", "转角"]
                    },
                    "北欧风格": {
                        "关键词": ["自然", "温馨", "实用", "舒适"],
                        "颜色": ["原木色", "白色", "浅灰"],
                        "材质": ["木材", "布艺", "羊毛"],
                        "形状": ["圆润", "简约"]
                    },
                    "中式风格": {
                        "关键词": ["传统", "雕花", "对称", "庄重"],
                        "颜色": ["红木色", "深棕色", "金色"],
                        "材质": ["实木", "丝绸", "棉麻"],
                        "形状": ["方正", "对称"]
                    }
                },
                "功能特征": {
                    "客厅沙发": ["大尺寸", "多人座", "舒适性"],
                    "单人沙发": ["小巧", "个人空间", "阅读"],
                    "转角沙发": ["L型", "空间利用", "分区"]
                },
                "材质特征": {
                    "真皮": ["光泽", "纹理", "高档"],
                    "布艺": ["柔软", "透气", "多色"],
                    "绒布": ["温暖", "舒适", "冬季"]
                }
            },
            "椅子": {
                "风格特征": {
                    "现代简约": {
                        "关键词": ["简洁", "功能", "线条"],
                        "颜色": ["单色", "对比色"],
                        "材质": ["塑料", "金属", "皮革"],
                        "形状": ["几何", "流线"]
                    },
                    "古典风格": {
                        "关键词": ["雕花", "装饰", "传统"],
                        "颜色": ["深色", "金色装饰"],
                        "材质": ["实木", "丝绸"],
                        "形状": ["曲线", "装饰性"]
                    }
                }
            },
            "桌子": {
                "风格特征": {
                    "现代简约": {
                        "关键词": ["简洁", "功能性", "几何"],
                        "颜色": ["白色", "黑色", "原木色"],
                        "材质": ["玻璃", "金属", "木材"],
                        "形状": ["矩形", "圆形", "椭圆"]
                    }
                },
                "功能特征": {
                    "餐桌": ["用餐", "聚会", "中心位置"],
                    "茶几": ["客厅", "休闲", "装饰"],
                    "办公桌": ["工作", "储物", "功能性"]
                }
            }
        }
    
    def analyze_furniture_detailed(self, image_path: str) -> Dict:
        """详细分析家具特征"""
        print(f"\n开始详细分析: {image_path}")
        
        # 基础检测
        image = self.image_processor.load_and_preprocess(image_path)
        if image is None:
            return {'error': '图片加载失败'}
        
        # YOLO检测
        detections = self.detector.detect_furniture(image_path)
        if not detections:
            return {'error': '未检测到家具'}
        
        print(f"检测到 {len(detections)} 件家具")
        
        # 详细分析每件家具
        detailed_results = []
        for i, detection in enumerate(detections):
            print(f"分析第 {i+1} 件家具: {detection['class_name']}")
            
            # 提取家具区域
            furniture_regions = self.detector.extract_furniture_regions(image_path)
            if i < len(furniture_regions):
                region_image, _ = furniture_regions[i]
                
                # 详细特征分析
                detailed_analysis = self._analyze_furniture_features(
                    region_image, detection['class_name'], detection
                )
                detailed_results.append(detailed_analysis)
        
        # 生成综合描述
        comprehensive_description = self._generate_comprehensive_description(detailed_results)
        
        return {
            'image_path': image_path,
            'total_items': len(detections),
            'detailed_analysis': detailed_results,
            'comprehensive_description': comprehensive_description,
            'analysis_timestamp': self._get_timestamp()
        }
    
    def _analyze_furniture_features(self, image: np.ndarray, furniture_type: str, detection: Dict) -> Dict:
        """分析单件家具的详细特征"""
        
        # 基础分析
        style_result = self.style_classifier.classify_style(image)
        material_result = self.material_classifier.classify_material(image)
        color_result = self.color_analyzer.analyze_colors(image)
        
        # 形状和结构分析
        shape_analysis = self._analyze_shape_structure(image)
        
        # 功能特征推断
        functional_features = self._infer_functional_features(
            furniture_type, style_result, material_result, shape_analysis
        )
        
        # 生成详细描述
        detailed_description = self._generate_detailed_description(
            furniture_type, style_result, material_result, color_result, 
            shape_analysis, functional_features
        )
        
        return {
            'furniture_type': furniture_type,
            'chinese_name': self._get_chinese_name(furniture_type),
            'confidence': detection['confidence'],
            'style_analysis': style_result,
            'material_analysis': material_result,
            'color_analysis': color_result,
            'shape_analysis': shape_analysis,
            'functional_features': functional_features,
            'detailed_description': detailed_description,
            'professional_tags': self._generate_professional_tags(
                furniture_type, style_result, material_result, functional_features
            )
        }
    
    def _analyze_shape_structure(self, image: np.ndarray) -> Dict:
        """分析家具的形状和结构特征"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        
        # 轮廓检测
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {'shape_type': '未知', 'structure_features': []}
        
        # 分析主要轮廓
        main_contour = max(contours, key=cv2.contourArea)
        
        # 计算轮廓特征
        area = cv2.contourArea(main_contour)
        perimeter = cv2.arcLength(main_contour, True)
        
        # 近似轮廓
        epsilon = 0.02 * perimeter
        approx = cv2.approxPolyDP(main_contour, epsilon, True)
        
        # 判断形状类型
        shape_type = self._classify_shape(len(approx), area, perimeter)
        
        # 分析结构特征
        structure_features = self._analyze_structure_features(image, main_contour)
        
        return {
            'shape_type': shape_type,
            'vertex_count': len(approx),
            'area_ratio': area / (image.shape[0] * image.shape[1]),
            'structure_features': structure_features,
            'geometric_properties': {
                'area': float(area),
                'perimeter': float(perimeter),
                'compactness': 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            }
        }
    
    def _classify_shape(self, vertex_count: int, area: float, perimeter: float) -> str:
        """根据顶点数量和几何特征分类形状"""
        if vertex_count == 3:
            return "三角形"
        elif vertex_count == 4:
            return "矩形/方形"
        elif vertex_count > 8:
            return "圆形/椭圆形"
        elif vertex_count > 4:
            return "多边形"
        else:
            return "不规则形状"
    
    def _analyze_structure_features(self, image: np.ndarray, contour) -> List[str]:
        """分析结构特征"""
        features = []
        
        # 计算边界框
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 1
        
        if aspect_ratio > 2:
            features.append("长条形")
        elif aspect_ratio < 0.5:
            features.append("高长形")
        else:
            features.append("方正形")
        
        # 分析对称性
        if self._check_symmetry(image):
            features.append("对称设计")
        
        # 分析线条特征
        if self._has_curved_lines(contour):
            features.append("曲线设计")
        else:
            features.append("直线设计")
        
        return features
    
    def _check_symmetry(self, image: np.ndarray) -> bool:
        """检查图像对称性"""
        h, w = image.shape[:2]
        left_half = image[:, :w//2]
        right_half = cv2.flip(image[:, w//2:], 1)
        
        # 调整尺寸
        min_width = min(left_half.shape[1], right_half.shape[1])
        left_half = left_half[:, :min_width]
        right_half = right_half[:, :min_width]
        
        # 计算相似度
        diff = cv2.absdiff(left_half, right_half)
        similarity = 1 - (np.mean(diff) / 255)
        
        return similarity > 0.7
    
    def _has_curved_lines(self, contour) -> bool:
        """检测是否有曲线"""
        # 简化轮廓
        epsilon = 0.01 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 如果简化后的点数明显少于原始点数，说明有曲线
        return len(approx) < len(contour) * 0.1
    
    def _infer_functional_features(self, furniture_type: str, style_result: Dict, 
                                 material_result: Dict, shape_analysis: Dict) -> List[str]:
        """推断功能特征"""
        features = []
        
        # 基于家具类型的功能特征
        type_features = {
            'couch': ['休息', '客厅', '多人座'],
            'chair': ['座椅', '单人', '支撑'],
            'dining table': ['用餐', '聚会', '平面'],
            'bed': ['睡眠', '卧室', '舒适']
        }
        
        if furniture_type in type_features:
            features.extend(type_features[furniture_type])
        
        # 基于材质的功能特征
        material = material_result.get('predicted_material', '')
        if 'leather' in material.lower():
            features.extend(['高档', '易清洁', '商务'])
        elif 'fabric' in material.lower():
            features.extend(['舒适', '透气', '家居'])
        elif 'wood' in material.lower():
            features.extend(['自然', '耐用', '传统'])
        
        # 基于形状的功能特征
        if '长条形' in shape_analysis.get('structure_features', []):
            features.append('空间利用')
        if '对称设计' in shape_analysis.get('structure_features', []):
            features.append('平衡美观')
        
        return list(set(features))  # 去重
    
    def _generate_detailed_description(self, furniture_type: str, style_result: Dict,
                                     material_result: Dict, color_result: Dict,
                                     shape_analysis: Dict, functional_features: List[str]) -> str:
        """生成详细的家具描述"""
        
        # 获取中文名称
        chinese_name = self._get_chinese_name(furniture_type)
        
        # 风格描述
        style = style_result.get('predicted_style', '现代')
        style_chinese = self._translate_style(style)
        
        # 材质描述
        material = material_result.get('predicted_material', '未知')
        material_chinese = self._translate_material(material)
        
        # 颜色描述
        primary_color = color_result.get('primary_color', '未知')
        
        # 形状描述
        shape_type = shape_analysis.get('shape_type', '常规')
        structure_features = shape_analysis.get('structure_features', [])
        
        # 构建描述
        description_parts = []
        
        # 风格 + 材质 + 颜色 + 家具类型
        description_parts.append(f"{style_chinese}风格")
        
        if material_chinese != '未知':
            description_parts.append(f"{material_chinese}材质")
        
        if primary_color != '未知':
            description_parts.append(f"{primary_color}色")
        
        # 添加功能特征
        if '客厅' in functional_features:
            description_parts.append("客厅")
        if '多人座' in functional_features:
            description_parts.append("多人")
        
        description_parts.append(chinese_name)
        
        # 添加结构特征
        if structure_features:
            if '曲线设计' in structure_features:
                description_parts.append("流线型设计")
            if '对称设计' in structure_features:
                description_parts.append("对称式")
        
        return "".join(description_parts)
    
    def _generate_professional_tags(self, furniture_type: str, style_result: Dict,
                                   material_result: Dict, functional_features: List[str]) -> List[str]:
        """生成专业标签"""
        tags = []
        
        # 风格标签
        style = style_result.get('predicted_style', '')
        if style:
            tags.append(f"{self._translate_style(style)}风格")
        
        # 材质标签
        material = material_result.get('predicted_material', '')
        if material:
            tags.append(f"{self._translate_material(material)}材质")
        
        # 功能标签
        tags.extend(functional_features)
        
        # 家具类型标签
        tags.append(self._get_chinese_name(furniture_type))
        
        return list(set(tags))
    
    def _get_chinese_name(self, furniture_type: str) -> str:
        """获取家具的中文名称"""
        name_mapping = {
            'couch': '沙发',
            'sofa': '沙发', 
            'chair': '椅子',
            'dining table': '餐桌',
            'table': '桌子',
            'bed': '床',
            'desk': '书桌',
            'cabinet': '柜子',
            'tv': '电视'
        }
        return name_mapping.get(furniture_type.lower(), furniture_type)
    
    def _translate_style(self, style: str) -> str:
        """翻译风格名称"""
        style_mapping = {
            'modern': '现代简约',
            'contemporary': '当代',
            'traditional': '传统',
            'chinese': '中式',
            'scandinavian': '北欧',
            'industrial': '工业'
        }
        return style_mapping.get(style.lower(), style)
    
    def _translate_material(self, material: str) -> str:
        """翻译材质名称"""
        material_mapping = {
            'leather': '真皮',
            'fabric': '布艺',
            'wood': '实木',
            'metal': '金属',
            'glass': '玻璃',
            'plastic': '塑料'
        }
        return material_mapping.get(material.lower(), material)
    
    def _generate_comprehensive_description(self, detailed_results: List[Dict]) -> str:
        """生成综合描述"""
        if not detailed_results:
            return "未检测到家具"
        
        descriptions = []
        for result in detailed_results:
            descriptions.append(result['detailed_description'])
        
        return "、".join(descriptions)
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main():
    """测试增强版分析器"""
    analyzer = EnhancedFurnitureAnalyzer()
    
    # 测试图片
    test_images = [
        "test_images/chair.jpg",
        "test_images/sofa.jpg", 
        "test_images/table.jpg"
    ]
    
    for image_path in test_images:
        if Path(image_path).exists():
            print(f"\n{'='*60}")
            print(f"测试图片: {image_path}")
            print('='*60)
            
            result = analyzer.analyze_furniture_detailed(image_path)
            
            if 'error' in result:
                print(f"错误: {result['error']}")
                continue
            
            print(f"综合描述: {result['comprehensive_description']}")
            print(f"检测数量: {result['total_items']} 件")
            
            for i, item in enumerate(result['detailed_analysis'], 1):
                print(f"\n第 {i} 件家具:")
                print(f"  详细描述: {item['detailed_description']}")
                print(f"  专业标签: {', '.join(item['professional_tags'])}")
                print(f"  功能特征: {', '.join(item['functional_features'])}")

if __name__ == "__main__":
    main()
