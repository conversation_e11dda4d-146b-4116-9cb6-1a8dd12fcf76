# 家具识别分析系统 (RecoImage)

一个基于YOLO和深度学习的智能家具识别分析系统，能够识别家具类型、风格、材质、颜色等多种属性。

## 功能特点

### 🔍 家具检测
- 使用YOLOv8模型检测图片中的家具对象
- 支持多种家具类型：沙发、椅子、桌子、床、柜子等
- 自动识别家具组合（如餐桌组合、客厅套装等）

### 🎨 风格分析
- **现代风格** - 简洁线条，几何造型
- **中式风格** - 传统中国设计元素
- **美式风格** - 实用主义设计
- **法式风格** - 优雅精致造型
- **中古风格** - 复古怀旧设计
- **宋式美学** - 简约雅致的东方美学

### 🧱 材质识别
- **木材** - 各种木质材料
- **金属** - 钢铁、铝合金等
- **布艺** - 织物面料
- **皮革** - 真皮、人造革
- **塑料** - 各种塑料材质
- **玻璃** - 透明、磨砂玻璃

### 🌈 颜色分析
- 提取主要颜色和色彩组合
- 分析色彩和谐度
- 评估亮度和饱和度

### 🏠 使用场景识别
- 客厅、卧室、餐厅、厨房、办公室等

## 安装说明

### 环境要求
- Python 3.8+
- CUDA支持的GPU（可选，用于加速）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd RecoImage
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **下载YOLO模型**
```bash
# YOLOv8模型会在首次运行时自动下载
```

## 使用方法

### 命令行使用

#### 分析单张图片
```bash
python main.py path/to/your/image.jpg
```

#### 批量分析
```bash
python main.py path/to/image/directory -b -o output_directory
```

#### 保存结果
```bash
python main.py image.jpg -o results/
```

### Web界面使用

启动Streamlit Web应用：
```bash
streamlit run web_app.py
```

然后在浏览器中打开 `http://localhost:8501`

### Python API使用

```python
from main import FurnitureAnalyzer

# 初始化分析器
analyzer = FurnitureAnalyzer()

# 分析图片
result = analyzer.analyze_image("furniture_image.jpg")

# 打印结果摘要
analyzer.print_result_summary(result)
```

## 项目结构

```
RecoImage/
├── main.py                 # 主程序入口
├── web_app.py             # Streamlit Web应用
├── requirements.txt       # 依赖包列表
├── config/
│   ├── __init__.py
│   └── settings.py        # 配置文件
├── models/
│   ├── __init__.py
│   ├── yolo_detector.py   # YOLO家具检测
│   ├── style_classifier.py # 风格分类器
│   ├── material_classifier.py # 材质分类器
│   └── color_analyzer.py  # 颜色分析器
├── utils/
│   ├── __init__.py
│   ├── image_processor.py # 图像处理工具
│   └── result_formatter.py # 结果格式化
├── tests/
│   └── test_detection.py  # 测试脚本
└── data/
    ├── sample_images/     # 示例图片
    └── models/           # 模型文件
```

## 输出格式

### JSON格式
```json
{
  "image_info": {
    "path": "image.jpg",
    "filename": "image.jpg",
    "analysis_timestamp": "2024-01-01T12:00:00"
  },
  "summary": {
    "total_furniture_items": 3,
    "is_furniture_set": true,
    "set_type": "dining_set",
    "dominant_style": "modern",
    "dominant_material": "wood",
    "dominant_color": "brown"
  },
  "furniture_details": [
    {
      "item_id": 1,
      "furniture_type": "dining table",
      "style": {
        "predicted_style": "modern",
        "confidence": 0.85
      },
      "material": {
        "predicted_material": "wood",
        "confidence": 0.92
      },
      "colors": {
        "primary_color": "brown",
        "dominant_colors": ["brown", "beige"]
      }
    }
  ]
}
```

### 文本报告
```
============================================================
FURNITURE ANALYSIS REPORT
============================================================
Image: dining_room.jpg
Analysis Date: 2024-01-01T12:00:00

SUMMARY
--------------------
Total Furniture Items: 3
Is Furniture Set: Yes
Set Type: dining_set
Dominant Style: modern
Dominant Material: wood
Dominant Color: brown
Primary Usage Scenario: dining_room
```

## 测试

运行测试脚本：
```bash
python tests/test_detection.py
```

## 性能优化

### GPU加速
确保安装了CUDA版本的PyTorch：
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 模型优化
- 使用较小的YOLO模型（如YOLOv8n）以提高速度
- 调整图像预处理尺寸
- 使用批处理模式处理多张图片

## 常见问题

### Q: 检测不到家具怎么办？
A: 
- 确保图片清晰，家具占据足够大的区域
- 调整置信度阈值设置
- 检查图片格式是否支持

### Q: 风格识别不准确？
A: 
- 系统基于视觉特征进行分析，可能需要更多训练数据
- 可以通过调整分类阈值来改善结果

### Q: 如何添加新的家具类型？
A: 
- 修改 `config/settings.py` 中的 `FURNITURE_CATEGORIES`
- 重新训练或微调YOLO模型

## 技术栈

- **深度学习框架**: PyTorch
- **目标检测**: YOLOv8 (Ultralytics)
- **图像处理**: OpenCV, PIL
- **颜色分析**: scikit-image, ColorThief
- **机器学习**: scikit-learn
- **Web界面**: Streamlit
- **数据处理**: NumPy, Pandas

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基本家具检测和分析功能
- 提供命令行和Web界面
