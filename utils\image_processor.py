"""
Image processing utilities
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance
from typing import <PERSON>ple, Optional
import os

from config.settings import MAX_IMAGE_SIZE, MIN_IMAGE_SIZE


class ImageProcessor:
    """Utility class for image processing operations"""
    
    def __init__(self):
        """Initialize the image processor"""
        self.max_size = MAX_IMAGE_SIZE
        self.min_size = MIN_IMAGE_SIZE
    
    def load_and_preprocess(self, image_path: str) -> Optional[np.ndarray]:
        """
        Load and preprocess an image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Preprocessed image as numpy array (BGR format) or None if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(image_path):
                print(f"Image file not found: {image_path}")
                return None
            
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                print(f"Failed to load image: {image_path}")
                return None
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            return processed_image
            
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for analysis
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Preprocessed image
        """
        # Resize image if needed
        image = self.resize_image(image)
        
        # Enhance image quality
        image = self.enhance_image(image)
        
        # Denoise image
        image = self.denoise_image(image)
        
        return image
    
    def resize_image(self, image: np.ndarray) -> np.ndarray:
        """
        Resize image to optimal size for processing
        
        Args:
            image: Input image
            
        Returns:
            Resized image
        """
        height, width = image.shape[:2]
        
        # Check if resizing is needed
        if width > self.max_size[0] or height > self.max_size[1]:
            # Calculate scaling factor
            scale_w = self.max_size[0] / width
            scale_h = self.max_size[1] / height
            scale = min(scale_w, scale_h)
            
            # Resize image
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        elif width < self.min_size[0] or height < self.min_size[1]:
            # Upscale small images
            scale_w = self.min_size[0] / width
            scale_h = self.min_size[1] / height
            scale = max(scale_w, scale_h)
            
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        return image
    
    def enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality
        
        Args:
            image: Input image
            
        Returns:
            Enhanced image
        """
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Convert back to OpenCV format
            enhanced_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return enhanced_image
            
        except Exception as e:
            print(f"Error enhancing image: {e}")
            return image
    
    def denoise_image(self, image: np.ndarray) -> np.ndarray:
        """
        Remove noise from image
        
        Args:
            image: Input image
            
        Returns:
            Denoised image
        """
        try:
            # Apply bilateral filter for noise reduction while preserving edges
            denoised = cv2.bilateralFilter(image, 9, 75, 75)
            return denoised
            
        except Exception as e:
            print(f"Error denoising image: {e}")
            return image
    
    def crop_region(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """
        Crop a region from the image
        
        Args:
            image: Input image
            bbox: Bounding box as (x1, y1, x2, y2)
            
        Returns:
            Cropped image region
        """
        x1, y1, x2, y2 = bbox
        
        # Ensure coordinates are within image bounds
        height, width = image.shape[:2]
        x1 = max(0, min(x1, width))
        y1 = max(0, min(y1, height))
        x2 = max(x1, min(x2, width))
        y2 = max(y1, min(y2, height))
        
        # Crop the region
        cropped = image[y1:y2, x1:x2]
        
        return cropped
    
    def create_mask(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """
        Create a binary mask for a region
        
        Args:
            image: Input image
            bbox: Bounding box as (x1, y1, x2, y2)
            
        Returns:
            Binary mask
        """
        height, width = image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        x1, y1, x2, y2 = bbox
        mask[y1:y2, x1:x2] = 255
        
        return mask
    
    def apply_mask(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        Apply a mask to an image
        
        Args:
            image: Input image
            mask: Binary mask
            
        Returns:
            Masked image
        """
        if len(image.shape) == 3:
            # Color image
            masked = cv2.bitwise_and(image, image, mask=mask)
        else:
            # Grayscale image
            masked = cv2.bitwise_and(image, mask)
        
        return masked
    
    def calculate_image_quality_score(self, image: np.ndarray) -> float:
        """
        Calculate a quality score for the image
        
        Args:
            image: Input image
            
        Returns:
            Quality score between 0 and 1
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Calculate sharpness using Laplacian variance
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_score = min(1.0, laplacian_var / 1000.0)
            
            # Calculate brightness score
            brightness = np.mean(gray)
            brightness_score = 1.0 - abs(brightness - 128) / 128.0
            
            # Calculate contrast score
            contrast = gray.std()
            contrast_score = min(1.0, contrast / 64.0)
            
            # Combine scores
            quality_score = (sharpness_score * 0.4 + 
                           brightness_score * 0.3 + 
                           contrast_score * 0.3)
            
            return float(quality_score)
            
        except Exception as e:
            print(f"Error calculating image quality: {e}")
            return 0.5
    
    def save_processed_image(self, image: np.ndarray, output_path: str) -> bool:
        """
        Save processed image to file
        
        Args:
            image: Image to save
            output_path: Output file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save image
            success = cv2.imwrite(output_path, image)
            
            return success
            
        except Exception as e:
            print(f"Error saving image to {output_path}: {e}")
            return False
