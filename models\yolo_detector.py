"""
YOLO-based furniture detection module
"""
import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>
from typing import List, Dict, Tu<PERSON>, Optional
from pathlib import Path
import torch

from config.settings import (
    YOLO_CONFIDENCE_THRESHOLD, 
    YOLO_IOU_THRESHOLD,
    FURNITURE_CATEGORIES
)


class FurnitureDetector:
    """YOLO-based furniture detector"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the furniture detector
        
        Args:
            model_path: Path to custom YOLO model, if None uses YOLOv8n
        """
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        if model_path and Path(model_path).exists():
            self.model = YOLO(model_path)
        else:
            # Use pre-trained YOLOv8 model
            self.model = YOLO('yolov8n.pt')
        
        self.confidence_threshold = YOLO_CONFIDENCE_THRESHOLD
        self.iou_threshold = YOLO_IOU_THRESHOLD
        
    def detect_furniture(self, image_path: str) -> List[Dict]:
        """
        Detect furniture in an image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            List of detected furniture objects with bounding boxes and confidence
        """
        try:
            # Run inference
            results = self.model(
                image_path,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                device=self.device
            )
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class name
                        class_id = int(box.cls[0])
                        class_name = self.model.names[class_id]
                        
                        # Filter for furniture-related objects
                        if self._is_furniture_related(class_name):
                            # Get bounding box coordinates
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            
                            detection = {
                                'class_name': class_name,
                                'confidence': confidence,
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'area': (x2 - x1) * (y2 - y1)
                            }
                            detections.append(detection)
            
            return sorted(detections, key=lambda x: x['confidence'], reverse=True)
            
        except Exception as e:
            print(f"Error in furniture detection: {e}")
            return []
    
    def _is_furniture_related(self, class_name: str) -> bool:
        """
        Check if detected class is furniture-related
        
        Args:
            class_name: Name of the detected class
            
        Returns:
            True if the class is furniture-related
        """
        furniture_keywords = [
            'chair', 'couch', 'bed', 'dining table', 'toilet', 'tv', 
            'laptop', 'mouse', 'remote', 'keyboard', 'cell phone',
            'microwave', 'oven', 'toaster', 'sink', 'refrigerator',
            'book', 'clock', 'vase', 'scissors', 'teddy bear',
            'hair drier', 'toothbrush'
        ]
        
        # Check if class name contains furniture keywords
        class_lower = class_name.lower()
        return any(keyword in class_lower for keyword in furniture_keywords)
    
    def extract_furniture_regions(self, image_path: str) -> List[Tuple[np.ndarray, Dict]]:
        """
        Extract furniture regions from image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            List of tuples containing (cropped_image, detection_info)
        """
        detections = self.detect_furniture(image_path)
        
        if not detections:
            return []
        
        # Load original image
        image = cv2.imread(image_path)
        if image is None:
            return []
        
        furniture_regions = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            
            # Crop the furniture region
            cropped = image[y1:y2, x1:x2]
            
            if cropped.size > 0:
                furniture_regions.append((cropped, detection))
        
        return furniture_regions
    
    def analyze_furniture_composition(self, detections: List[Dict]) -> Dict:
        """
        Analyze if furniture forms a composition/set
        
        Args:
            detections: List of furniture detections
            
        Returns:
            Analysis of furniture composition
        """
        if len(detections) < 2:
            return {
                'is_composition': False,
                'composition_type': 'single_item',
                'item_count': len(detections)
            }
        
        # Group similar furniture types
        furniture_groups = {}
        for detection in detections:
            class_name = detection['class_name']
            if class_name not in furniture_groups:
                furniture_groups[class_name] = []
            furniture_groups[class_name].append(detection)
        
        # Determine composition type
        composition_type = 'mixed_furniture'
        
        # Check for common furniture sets
        if 'dining table' in furniture_groups and 'chair' in furniture_groups:
            if len(furniture_groups['chair']) >= 2:
                composition_type = 'dining_set'
        elif 'couch' in furniture_groups and len(furniture_groups) > 1:
            composition_type = 'living_room_set'
        elif 'bed' in furniture_groups and len(furniture_groups) > 1:
            composition_type = 'bedroom_set'
        
        return {
            'is_composition': len(detections) > 1,
            'composition_type': composition_type,
            'item_count': len(detections),
            'furniture_groups': {k: len(v) for k, v in furniture_groups.items()}
        }
