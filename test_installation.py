#!/usr/bin/env python3
"""
测试依赖安装是否成功的脚本
"""

def test_imports():
    """测试所有主要依赖的导入"""
    print("开始测试依赖导入...")
    
    try:
        # 基础依赖
        import numpy as np
        print("✓ NumPy 导入成功")
        
        import cv2
        print("✓ OpenCV 导入成功")
        
        import pandas as pd
        print("✓ Pandas 导入成功")
        
        from PIL import Image
        print("✓ Pillow 导入成功")
        
        # 深度学习框架
        import torch
        print(f"✓ PyTorch 导入成功 (版本: {torch.__version__})")
        
        import torchvision
        print(f"✓ TorchVision 导入成功 (版本: {torchvision.__version__})")
        
        # YOLO模型
        from ultralytics import YOLO
        print("✓ Ultralytics YOLO 导入成功")
        
        # 图像处理
        import skimage
        print("✓ Scikit-image 导入成功")
        
        from colorthief import ColorThief
        print("✓ ColorThief 导入成功")
        
        from sklearn import __version__ as sklearn_version
        print(f"✓ Scikit-learn 导入成功 (版本: {sklearn_version})")
        
        # Web界面
        import streamlit as st
        print(f"✓ Streamlit 导入成功 (版本: {st.__version__})")
        
        # 工具库
        import tqdm
        print("✓ tqdm 导入成功")
        
        import requests
        print("✓ requests 导入成功")
        
        import yaml
        print("✓ PyYAML 导入成功")
        
        # 可视化
        import matplotlib.pyplot as plt
        print("✓ Matplotlib 导入成功")
        
        print("\n所有依赖导入测试通过！ ✅")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_project_modules():
    """测试项目模块导入"""
    print("\n开始测试项目模块...")
    
    try:
        # 测试配置模块
        from config.settings import YOLO_CONFIDENCE_THRESHOLD
        print("✓ 配置模块导入成功")
        
        # 测试模型模块
        from models.yolo_detector import FurnitureDetector
        print("✓ YOLO检测器模块导入成功")
        
        from models.color_analyzer import ColorAnalyzer
        print("✓ 颜色分析器模块导入成功")
        
        from models.material_classifier import MaterialClassifier
        print("✓ 材质分类器模块导入成功")
        
        from models.style_classifier import StyleClassifier
        print("✓ 风格分类器模块导入成功")
        
        # 测试工具模块
        from utils.image_processor import ImageProcessor
        print("✓ 图像处理器模块导入成功")
        
        from utils.result_formatter import ResultFormatter
        print("✓ 结果格式化器模块导入成功")
        
        print("\n所有项目模块导入测试通过！ ✅")
        return True
        
    except ImportError as e:
        print(f"❌ 项目模块导入失败: {e}")
        return False

def test_gpu_availability():
    """测试GPU可用性"""
    print("\n检查GPU可用性...")
    
    import torch
    
    if torch.cuda.is_available():
        print(f"✓ CUDA 可用")
        print(f"  GPU 设备数量: {torch.cuda.device_count()}")
        print(f"  当前GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠ CUDA 不可用，将使用CPU")
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("RecoImage 依赖安装测试")
    print("=" * 50)
    
    # 测试依赖导入
    deps_ok = test_imports()
    
    # 测试项目模块
    modules_ok = test_project_modules()
    
    # 测试GPU
    test_gpu_availability()
    
    print("\n" + "=" * 50)
    if deps_ok and modules_ok:
        print("🎉 所有测试通过！项目依赖安装成功！")
        print("\n可以使用以下命令启动Web应用:")
        print("py -3.13 -m streamlit run web_app.py")
    else:
        print("❌ 部分测试失败，请检查依赖安装")
    print("=" * 50)

if __name__ == "__main__":
    main()
