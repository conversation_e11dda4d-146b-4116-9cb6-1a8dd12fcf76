# 安装指南

由于网络连接问题，这里提供详细的手动安装指南。

## 方法一：使用国内镜像源（推荐）

### 1. 配置pip使用国内镜像源

创建或编辑pip配置文件：

**Windows:**
```
%APPDATA%\pip\pip.ini
```

**Linux/Mac:**
```
~/.pip/pip.conf
```

添加以下内容：
```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
```

### 2. 分步安装依赖

```bash
# 1. 安装基础依赖
pip install numpy opencv-python Pillow pandas

# 2. 安装PyTorch (根据您的系统选择)
# CPU版本
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# GPU版本 (如果有CUDA)
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# 3. 安装YOLO
pip install ultralytics

# 4. 安装图像处理库
pip install scikit-image colorthief scikit-learn

# 5. 安装工具库
pip install tqdm requests pyyaml

# 6. 安装Web界面 (可选)
pip install streamlit

# 7. 安装可视化库 (可选)
pip install matplotlib
```

## 方法二：使用conda (推荐有conda环境的用户)

```bash
# 创建新环境
conda create -n furniture_analysis python=3.8

# 激活环境
conda activate furniture_analysis

# 安装基础依赖
conda install numpy opencv pandas pillow scikit-learn matplotlib

# 安装PyTorch
conda install pytorch torchvision -c pytorch

# 使用pip安装其他依赖
pip install ultralytics scikit-image colorthief streamlit tqdm pyyaml
```

## 方法三：离线安装

如果网络问题持续，可以：

1. 在有网络的机器上下载wheel文件
2. 传输到目标机器
3. 使用 `pip install *.whl` 安装

## 验证安装

运行以下命令验证安装：

```python
python -c "
import numpy as np
import cv2
import torch
from PIL import Image
print('基础依赖安装成功!')
"
```

```python
python -c "
from ultralytics import YOLO
print('YOLO安装成功!')
"
```

## 常见问题

### Q: torch安装失败
A: 尝试安装CPU版本：
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### Q: opencv-python安装失败
A: 尝试安装headless版本：
```bash
pip install opencv-python-headless
```

### Q: 网络超时
A: 增加超时时间：
```bash
pip install --timeout 1000 package_name
```

## 最小依赖运行

如果只想测试基本功能，最少需要：
- numpy
- opencv-python
- Pillow
- ultralytics

其他依赖可以后续添加。
