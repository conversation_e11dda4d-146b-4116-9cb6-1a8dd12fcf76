"""
Color analysis module for furniture images
"""
import cv2
import numpy as np
from typing import Dict, List, Tuple
from sklearn.cluster import KMeans
from colorthief import ColorThief
from PIL import Image
import io

from config.settings import COLOR_CATEGORIES


class ColorAnalyzer:
    """Analyze colors in furniture images"""

    def __init__(self):
        """Initialize the color analyzer"""
        self.color_categories = COLOR_CATEGORIES

        # Define color ranges in HSV for better color detection
        self.color_ranges = {
            'red': [(0, 50, 50), (10, 255, 255), (170, 50, 50), (180, 255, 255)],
            'orange': [(10, 50, 50), (25, 255, 255)],
            'yellow': [(25, 50, 50), (35, 255, 255)],
            'green': [(35, 50, 50), (85, 255, 255)],
            'blue': [(85, 50, 50), (125, 255, 255)],
            'purple': [(125, 50, 50), (155, 255, 255)],
            'pink': [(155, 50, 50), (170, 255, 255)],
            'white': [(0, 0, 200), (180, 30, 255)],
            'black': [(0, 0, 0), (180, 255, 50)],
            'gray': [(0, 0, 50), (180, 30, 200)],
            'brown': [(8, 50, 20), (20, 255, 200)],
            'beige': [(15, 30, 180), (30, 100, 255)],
            'cream': [(20, 20, 200), (30, 60, 255)]
        }

    def analyze_colors(self, image: np.ndarray) -> Dict:
        """
        Comprehensive color analysis of furniture image

        Args:
            image: Input image as numpy array (BGR format)

        Returns:
            Dictionary with color analysis results
        """
        try:
            # Convert BGR to RGB
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image

            # Extract dominant colors using multiple methods
            dominant_colors_kmeans = self._extract_dominant_colors_kmeans(image_rgb)
            dominant_colors_colorthief = self._extract_dominant_colors_colorthief(image_rgb)

            # Categorize colors
            color_categories = self._categorize_colors(dominant_colors_kmeans)

            # Calculate color statistics
            color_stats = self._calculate_color_statistics(image_rgb)

            # Analyze color harmony
            color_harmony = self._analyze_color_harmony(dominant_colors_kmeans)

            # Determine primary and secondary colors
            primary_color = self._determine_primary_color(color_categories)

            return {
                'dominant_colors_rgb': dominant_colors_kmeans,
                'dominant_colors_names': color_categories,
                'primary_color': primary_color,
                'color_statistics': color_stats,
                'color_harmony': color_harmony,
                'color_distribution': self._calculate_color_distribution(image_rgb),
                'brightness_level': self._calculate_brightness_level(image_rgb),
                'saturation_level': self._calculate_saturation_level(image_rgb)
            }

        except Exception as e:
            print(f"Error in color analysis: {e}")
            return self._fallback_color_analysis(image)

    def _extract_dominant_colors_kmeans(self, image: np.ndarray, k: int = 5) -> List[Tuple[int, int, int]]:
        """Extract dominant colors using K-means clustering"""
        # Resize image for faster processing
        small_image = cv2.resize(image, (150, 150))

        # Reshape image to be a list of pixels
        pixels = small_image.reshape(-1, 3)

        # Apply K-means clustering
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(pixels)

        # Get cluster centers (dominant colors)
        colors = kmeans.cluster_centers_.astype(int)

        # Get cluster sizes to sort by dominance
        labels = kmeans.labels_
        label_counts = np.bincount(labels)

        # Sort colors by frequency
        sorted_indices = np.argsort(label_counts)[::-1]
        dominant_colors = [tuple(colors[i]) for i in sorted_indices]

        return dominant_colors

    def _extract_dominant_colors_colorthief(self, image: np.ndarray) -> List[Tuple[int, int, int]]:
        """Extract dominant colors using ColorThief library"""
        try:
            # Convert numpy array to PIL Image
            pil_image = Image.fromarray(image)

            # Save to bytes buffer
            img_buffer = io.BytesIO()
            pil_image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # Use ColorThief
            color_thief = ColorThief(img_buffer)

            # Get dominant color
            dominant_color = color_thief.get_color(quality=1)

            # Get color palette
            try:
                palette = color_thief.get_palette(color_count=5, quality=1)
            except:
                palette = [dominant_color]

            return palette

        except Exception as e:
            print(f"ColorThief extraction failed: {e}")
            return [(128, 128, 128)]  # Default gray

    def _categorize_colors(self, rgb_colors: List[Tuple[int, int, int]]) -> List[str]:
        """Categorize RGB colors into named color categories"""
        categorized_colors = []

        for rgb_color in rgb_colors:
            # Convert RGB to HSV for better color matching
            rgb_array = np.uint8([[rgb_color]])
            hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2HSV)[0][0]

            # Find best matching color category
            best_match = self._find_closest_color_category(hsv_color)
            categorized_colors.append(best_match)

        return categorized_colors

    def _find_closest_color_category(self, hsv_color: np.ndarray) -> str:
        """Find the closest color category for an HSV color"""
        h, s, v = hsv_color

        # Check each color range
        for color_name, ranges in self.color_ranges.items():
            if len(ranges) == 2:  # Single range
                lower, upper = ranges
                if (lower[0] <= h <= upper[0] and
                    lower[1] <= s <= upper[1] and
                    lower[2] <= v <= upper[2]):
                    return color_name
            elif len(ranges) == 4:  # Two ranges (for red which wraps around)
                lower1, upper1, lower2, upper2 = ranges
                if ((lower1[0] <= h <= upper1[0] and
                     lower1[1] <= s <= upper1[1] and
                     lower1[2] <= v <= upper1[2]) or
                    (lower2[0] <= h <= upper2[0] and
                     lower2[1] <= s <= upper2[1] and
                     lower2[2] <= v <= upper2[2])):
                    return color_name

        # Default categorization based on HSV values
        if s < 30:  # Low saturation
            if v > 200:
                return 'white'
            elif v < 50:
                return 'black'
            else:
                return 'gray'
        else:  # High saturation
            if 0 <= h < 15 or 165 <= h <= 180:
                return 'red'
            elif 15 <= h < 30:
                return 'orange'
            elif 30 <= h < 45:
                return 'yellow'
            elif 45 <= h < 85:
                return 'green'
            elif 85 <= h < 125:
                return 'blue'
            else:
                return 'purple'

    def _calculate_color_statistics(self, image: np.ndarray) -> Dict:
        """Calculate various color statistics"""
        # Convert to different color spaces
        hsv_image = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        lab_image = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)

        stats = {
            'mean_rgb': np.mean(image, axis=(0, 1)).tolist(),
            'std_rgb': np.std(image, axis=(0, 1)).tolist(),
            'mean_hsv': np.mean(hsv_image, axis=(0, 1)).tolist(),
            'std_hsv': np.std(hsv_image, axis=(0, 1)).tolist(),
            'color_variance': float(np.var(image)),
            'color_range': {
                'min_rgb': np.min(image, axis=(0, 1)).tolist(),
                'max_rgb': np.max(image, axis=(0, 1)).tolist()
            }
        }

        return stats

    def _analyze_color_harmony(self, colors: List[Tuple[int, int, int]]) -> Dict:
        """Analyze color harmony and relationships"""
        if len(colors) < 2:
            return {'harmony_type': 'monochromatic', 'harmony_score': 1.0}

        # Convert colors to HSV for harmony analysis
        hsv_colors = []
        for color in colors[:3]:  # Analyze top 3 colors
            rgb_array = np.uint8([[color]])
            hsv_color = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2HSV)[0][0]
            hsv_colors.append(hsv_color)

        # Calculate hue differences with overflow protection
        hues = [float(hsv[0]) for hsv in hsv_colors]  # 确保是float类型
        hue_diffs = []
        for i in range(len(hues)):
            for j in range(i + 1, len(hues)):
                # 使用numpy的安全计算避免溢出
                diff = float(np.abs(hues[i] - hues[j]))
                # Handle hue wrap-around
                diff = min(diff, 180.0 - diff)
                hue_diffs.append(diff)

        # Determine harmony type
        avg_hue_diff = np.mean(hue_diffs) if hue_diffs else 0

        if avg_hue_diff < 30:
            harmony_type = 'monochromatic'
            harmony_score = 0.9
        elif 50 < avg_hue_diff < 70:
            harmony_type = 'analogous'
            harmony_score = 0.8
        elif 110 < avg_hue_diff < 130:
            harmony_type = 'complementary'
            harmony_score = 0.7
        elif 80 < avg_hue_diff < 100:
            harmony_type = 'triadic'
            harmony_score = 0.6
        else:
            harmony_type = 'complex'
            harmony_score = 0.5

        return {
            'harmony_type': harmony_type,
            'harmony_score': harmony_score,
            'average_hue_difference': float(avg_hue_diff)
        }

    def _determine_primary_color(self, color_categories: List[str]) -> str:
        """Determine the primary color from categorized colors"""
        if not color_categories:
            return 'unknown'

        # Count occurrences of each color category
        color_counts = {}
        for color in color_categories:
            color_counts[color] = color_counts.get(color, 0) + 1

        # Return most frequent color
        primary_color = max(color_counts.items(), key=lambda x: x[1])[0]
        return primary_color

    def _calculate_color_distribution(self, image: np.ndarray) -> Dict:
        """Calculate distribution of colors across the image"""
        # Convert to HSV
        hsv_image = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)

        # Calculate histograms
        hist_h = cv2.calcHist([hsv_image], [0], None, [180], [0, 180])
        hist_s = cv2.calcHist([hsv_image], [1], None, [256], [0, 256])
        hist_v = cv2.calcHist([hsv_image], [2], None, [256], [0, 256])

        return {
            'hue_distribution': hist_h.flatten().tolist(),
            'saturation_distribution': hist_s.flatten().tolist(),
            'value_distribution': hist_v.flatten().tolist()
        }

    def _calculate_brightness_level(self, image: np.ndarray) -> str:
        """Calculate overall brightness level"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        avg_brightness = np.mean(gray)

        if avg_brightness < 85:
            return 'dark'
        elif avg_brightness < 170:
            return 'medium'
        else:
            return 'bright'

    def _calculate_saturation_level(self, image: np.ndarray) -> str:
        """Calculate overall saturation level"""
        # Convert to HSV
        hsv_image = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        avg_saturation = np.mean(hsv_image[:, :, 1])

        if avg_saturation < 85:
            return 'low'
        elif avg_saturation < 170:
            return 'medium'
        else:
            return 'high'

    def _fallback_color_analysis(self, image: np.ndarray) -> Dict:
        """Fallback color analysis when main analysis fails"""
        # Simple color analysis
        mean_color = np.mean(image, axis=(0, 1))

        return {
            'dominant_colors_rgb': [tuple(mean_color.astype(int))],
            'dominant_colors_names': ['gray'],
            'primary_color': 'gray',
            'color_statistics': {'mean_rgb': mean_color.tolist()},
            'color_harmony': {'harmony_type': 'unknown', 'harmony_score': 0.5},
            'brightness_level': 'medium',
            'saturation_level': 'medium',
            'method': 'fallback'
        }
