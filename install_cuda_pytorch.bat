@echo off
echo ========================================
echo RecoImage CUDA PyTorch 安装脚本
echo ========================================
echo.
echo 检测到您的GPU: NVIDIA GeForce RTX 4060 Ti (CUDA 12.9)
echo 将安装支持CUDA的PyTorch版本以获得更好的性能
echo.
echo 注意: 此过程将下载约2.5GB的文件，请确保网络连接稳定
echo.
pause

echo.
echo 步骤1: 卸载当前CPU版本的PyTorch...
py -3.13 -m pip uninstall torch torchvision -y

echo.
echo 步骤2: 安装CUDA 12.1版本的PyTorch...
echo 这可能需要几分钟时间，请耐心等待...
py -3.13 -m pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121

echo.
echo 步骤3: 验证CUDA安装...
py -3.13 -c "import torch; print('PyTorch版本:', torch.__version__); print('CUDA可用:', torch.cuda.is_available()); print('GPU设备:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')"

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 如果看到 "CUDA可用: True"，说明GPU支持已成功启用
echo 现在您可以享受GPU加速的图像识别性能了！
echo.
pause
